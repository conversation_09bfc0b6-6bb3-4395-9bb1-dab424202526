/**
 * 星通互娱登录事件服务
 */

import { getDb } from '@/db'
import { xthyLoginData } from '@/schema'
import { eq, and, gte, lte, desc, count, sql } from 'drizzle-orm'
import { logger } from '@/lib/logger'
import { getChinaTime } from '@/utils/time'
import type { 
  NewXthyLoginData, 
  XthyLoginData, 
  CreateXthyLoginRequest,
  UpdateXthyLoginRequest,
  XthyLoginQueryParams,
  XthyLoginStats
} from '@/types/xthy-login'

/**
 * 创建登录事件记录
 */
export async function createLoginEvent(data: CreateXthyLoginRequest): Promise<XthyLoginData> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const now = getChinaTime()
  
  const insertData: NewXthyLoginData = {
    appId: data.appId,
    openId: data.openId,
    clickId: data.clickId,
    adId: data.adId,
    clickTime: data.clickTime ? new Date(data.clickTime) : undefined,
    platform: data.platform,
    createTime: now,
    updateTime: now,
  }

  const [result] = await db
    .insert(xthyLoginData)
    .values(insertData)
    .execute()

  logger.info('创建登录事件记录', { 
    openId: data.openId, 
    clickId: data.clickId,
    insertId: result.insertId 
  })

  // 查询并返回创建的记录
  const [created] = await db
    .select()
    .from(xthyLoginData)
    .where(eq(xthyLoginData.nonId, Number(result.insertId)))
    .execute()

  return created
}

/**
 * 根据 openId 查询登录事件
 */
export async function getLoginEventsByOpenId(openId: string): Promise<XthyLoginData[]> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  return await db
    .select()
    .from(xthyLoginData)
    .where(eq(xthyLoginData.openId, openId))
    .orderBy(desc(xthyLoginData.createTime))
    .execute()
}

/**
 * 根据 clickId 查询登录事件
 */
export async function getLoginEventByClickId(clickId: string): Promise<XthyLoginData | null> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const [result] = await db
    .select()
    .from(xthyLoginData)
    .where(eq(xthyLoginData.clickId, clickId))
    .execute()

  return result || null
}

/**
 * 更新登录事件（主要用于回传状态）
 */
export async function updateLoginEvent(
  nonId: number, 
  data: UpdateXthyLoginRequest
): Promise<XthyLoginData | null> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const updateData: Partial<NewXthyLoginData> = {
    ...data,
    updateTime: getChinaTime(),
  }

  if (data.postBackTime) {
    updateData.postBackTime = new Date(data.postBackTime)
  }

  await db
    .update(xthyLoginData)
    .set(updateData)
    .where(eq(xthyLoginData.nonId, nonId))
    .execute()

  logger.info('更新登录事件记录', { nonId, ...data })

  // 查询并返回更新后的记录
  const [updated] = await db
    .select()
    .from(xthyLoginData)
    .where(eq(xthyLoginData.nonId, nonId))
    .execute()

  return updated || null
}

/**
 * 标记 clickId 已回传
 */
export async function markClickIdCallback(clickId: string): Promise<boolean> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const now = getChinaTime()

  const [result] = await db
    .update(xthyLoginData)
    .set({
      clickIdCallbackFlag: 'Y',
      postBackTime: now,
      updateTime: now,
    })
    .where(eq(xthyLoginData.clickId, clickId))
    .execute()

  logger.info('标记clickId已回传', { clickId, affectedRows: result.affectedRows })

  return result.affectedRows > 0
}

/**
 * 分页查询登录事件
 */
export async function queryLoginEvents(params: XthyLoginQueryParams): Promise<{
  data: XthyLoginData[]
  total: number
  page: number
  pageSize: number
}> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const {
    openId,
    clickId,
    appId,
    platform,
    clickIdCallbackFlag,
    startTime,
    endTime,
    page = 1,
    pageSize = 20
  } = params

  // 构建查询条件
  const conditions = []
  
  if (openId) conditions.push(eq(xthyLoginData.openId, openId))
  if (clickId) conditions.push(eq(xthyLoginData.clickId, clickId))
  if (appId) conditions.push(eq(xthyLoginData.appId, appId))
  if (platform) conditions.push(eq(xthyLoginData.platform, platform))
  if (clickIdCallbackFlag) conditions.push(eq(xthyLoginData.clickIdCallbackFlag, clickIdCallbackFlag))
  if (startTime) conditions.push(gte(xthyLoginData.createTime, new Date(startTime)))
  if (endTime) conditions.push(lte(xthyLoginData.createTime, new Date(endTime)))

  const whereClause = conditions.length > 0 ? and(...conditions) : undefined

  // 查询总数
  const [{ total }] = await db
    .select({ total: count() })
    .from(xthyLoginData)
    .where(whereClause)
    .execute()

  // 查询数据
  const data = await db
    .select()
    .from(xthyLoginData)
    .where(whereClause)
    .orderBy(desc(xthyLoginData.createTime))
    .limit(pageSize)
    .offset((page - 1) * pageSize)
    .execute()

  return {
    data,
    total,
    page,
    pageSize
  }
}

/**
 * 获取登录事件统计
 */
export async function getLoginStats(
  startTime?: Date,
  endTime?: Date
): Promise<XthyLoginStats> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const conditions = []
  if (startTime) conditions.push(gte(xthyLoginData.createTime, startTime))
  if (endTime) conditions.push(lte(xthyLoginData.createTime, endTime))
  const whereClause = conditions.length > 0 ? and(...conditions) : undefined

  // 总登录数
  const [{ totalLogins }] = await db
    .select({ totalLogins: count() })
    .from(xthyLoginData)
    .where(whereClause)
    .execute()

  // 回传数
  const [{ callbackCount }] = await db
    .select({ callbackCount: count() })
    .from(xthyLoginData)
    .where(and(
      eq(xthyLoginData.clickIdCallbackFlag, 'Y'),
      ...(whereClause ? [whereClause] : [])
    ))
    .execute()

  // 平台统计
  const platformStats = await db
    .select({
      platform: xthyLoginData.platform,
      count: count()
    })
    .from(xthyLoginData)
    .where(whereClause)
    .groupBy(xthyLoginData.platform)
    .execute()

  return {
    totalLogins,
    callbackCount,
    callbackRate: totalLogins > 0 ? (callbackCount / totalLogins) * 100 : 0,
    platformStats: platformStats.map(stat => ({
      platform: stat.platform || 'unknown',
      count: stat.count
    }))
  }
}
