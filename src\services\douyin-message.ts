import { getDb } from '../db'
import { userSubscribe, scheduledTasks } from '../schema'
import { eq, and, lt } from 'drizzle-orm'
import { getAccessToken } from '@/redis'
import { formatChinaTime, getChinaTime } from '@/utils/time'
import { log, schedulerLogger } from '@/lib/logger'
import { BaseTaskResult } from '@/types/task-message'
import { ScheduledMessageTask } from '@/config'
import { Consumer, Producer } from 'kafkajs'
import { topics } from '@/config/kafka'

interface DouyinMessageData {
  access_token: string
  tpl_id: string
  app_id: string
  open_id: string
  data?: Record<string,string>
  page?:string
}

interface DouyinMessageResponse {
  err_no: number
  err_tips: string
}

/**
 * 发送抖音订阅消息
 * @param accessToken 小程序access_token
 * @param messageData 消息数据
 */
export async function sendDouyinSubscribeMessage(messageData: DouyinMessageData
): Promise<DouyinMessageResponse> {
  const url = `https://developer.toutiao.com/api/apps/subscribe_notification/developer/v1/notify`
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(messageData)
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json() as DouyinMessageResponse
    return result
  } catch (error) {
    console.error('发送抖音订阅消息失败:', error)
    throw error
  }
}

export async function sendBatchSubscribeMessagesSingle({appId,page,messageData,tplId,platform},{openId,id,count}: {
  id: string;
  openId: string;
  count: number;
}){
  try {
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
    const accessToken = await getAccessToken(appId) 
    if (!accessToken) {
      throw new Error(`未找到平台 ${platform} 应用 ${appId} 的 access_token`)
    }
    
    const result = await sendDouyinSubscribeMessage({
      access_token: accessToken,
      app_id: appId,
      open_id: openId,
      page: page,
      data: messageData,
      tpl_id: tplId
    })
    
    // 成功发送后更新计数和领取时间
    if (result.err_no === 0 && count !== null) {
      const updateTime = getChinaTime()
      await db
        .update(userSubscribe)
        .set({
          count: (count || 0) + 1,
          lastReceivedAt: updateTime,
          updatedAt: updateTime
        })
        .where(eq(userSubscribe.id, id))
        .execute()
    }
    console.log(`发送给用户 ${openId}: ${result.err_no === 0 ? '成功' : result.err_tips}`)

    return {
      openId: openId,
      success: result.err_no,
      error:  result.err_tips 
    }
  } catch (error) {
    const accessToken = "1e8da803e7f25ceecee6e96e6b9f3b9309ea53ae"
    console.error(`发送给用户 ${openId} 失败:`, error)
    return {
      appId,
      tplId,
      platform,
      id,
      count,
      messageData,
      page,
      accessToken,
      openId: openId,
      success: false,
      error: error instanceof Error ? error : '未知错误'
    }
  }
}

/**
 * 批量发送订阅消息给符合条件的用户
 * @param tplId 模板ID
 * @param messageData 消息内容数据
 * @param accessToken 访问令牌
 */
export async function sendBatchSubscribeMessages(
task:ScheduledMessageTask
): Promise<BaseTaskResult> {
  const {
    tplId,
    messageData,
    page,
    max
  } = task

  log.task.start(tplId, '开始执行批量消息推送任务', {
    platform: task.platform,
    maxUsers: max
  })
  const db = await getDb()
  if (!db) {
    throw new Error('数据库未初始化')
  }

  // 查询已接受订阅的用户，同时考虑用户的上次领取时间
  const allSubscribers = await db
    .select()
    .from(userSubscribe)
    .where(and(
      eq(userSubscribe.tplId, tplId),
      eq(userSubscribe.status, 'accept'),
      lt(userSubscribe.count, max)
    ))
    .execute()

  // 获取任务信息（用于判断任务类型）
  const taskInfo = await db
    .select()
    .from(scheduledTasks)
    .where(eq(scheduledTasks.tplId, tplId))
    .limit(1)
    .execute()

  if (taskInfo.length === 0) {
    throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
  }

  const taskRecord = taskInfo[0]
  const now = getChinaTime()

  // 过滤出可以接收消息的用户（考虑个人领取间隔）
  const subscribers = allSubscribers.filter(subscriber => {
    // 如果用户从未领取过，可以发送
    if (!subscriber.lastReceivedAt) {
      return true
    }

    // 检查是否已经过了一天（跨日期）
    const lastReceiveDate = new Date(subscriber.lastReceivedAt)
    const currentDate = now

    // 比较日期部分，只要不是同一天就可以发送
    const lastDateString = lastReceiveDate.toDateString()
    const currentDateString = currentDate.toDateString()

    return lastDateString !== currentDateString
  })

  log.task.progress(tplId, '查询用户订阅完成', {
    totalSubscribers: allSubscribers.length,
    validSubscribers: subscribers.length
  })

  if (subscribers.length === 0) {
    return {
      totalUsers: 0,
      results: [],
      message: '没有符合条件的用户'
    }
  }

  const results = []
  const accessToken = await getAccessToken(task.appId) 
  if (!accessToken) {
    throw new Error(`未找到平台 ${task.platform} 应用 ${task.appId} 的 access_token`)
  }
   const kafkaEnabled = process.env.KAFKA_ENABLED === 'true' && process.env.SCHEDULER_MODE === 'kafka'
  for (const subscriber of subscribers) {
  if (kafkaEnabled) {
    await sendMessageToKafka({task,subscriber,taskRecord,accessToken})
   } 
   else {  
     const result = await sendDirectMessage(task,subscriber,taskRecord,accessToken)
     results.push(result)
  }
  }
  log.task.complete(tplId, '同步批量消息推送任务完成', {
    totalUsers: subscribers.length,
    // successCount: results.filter(r => r.err_no === 0).length,
    // failCount: results.filter(r => r.err_no !== 0).length
  })

  return {
    totalUsers: subscribers.length,
    results,
    message:""
  }
}

export async function sendDirectMessage(task:ScheduledMessageTask,subscriber,taskRecord,accessToken:string) {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库未初始化')
  }
  const result = await sendDouyinSubscribeMessage({
    access_token: accessToken,
    app_id: task.appId,
    open_id:subscriber.openId,
    page: task.page,
    data: task.messageData.at(subscriber.count || 0) as Record<string,string>,
    tpl_id:task.tplId
  })
  
  // 根据任务类型和发送结果处理
  // 检查 meta.type 是否为 single
  const metaType = taskRecord.meta && typeof taskRecord.meta === 'object'
    ? (taskRecord.meta as any).type
    : null
  const isSingleTask = metaType === 'single'

  if (isSingleTask) {
    await db
      .delete(userSubscribe)
      .where(eq(userSubscribe.id, subscriber.id))
      .execute()

    if (result.err_no === 0) {
      log.task.success(task.tplId, `发送给用户 ${subscriber.openId}: 成功 (单次任务，已删除订阅记录)`, {
        openId: subscriber.openId,
        taskType: 'single'
      })
    } else {
      log.task.error(task.tplId, `发送给用户 ${subscriber.openId}: 失败 (单次任务，已删除订阅记录)`, {
        openId: subscriber.openId,
        errorCode: result.err_no,
        errorMessage: result.err_tips,
        taskType: 'single'
      })
    }
  } else {
    if (result.err_no === 0) {
      const newCount = (subscriber.count || 0) + 1
      const updateTime = getChinaTime()

      await db
        .update(userSubscribe)
        .set({
          count: newCount,
          updatedAt: updateTime,
          lastReceivedAt: updateTime 
        })
        .where(eq(userSubscribe.id, subscriber.id))
        .execute()

      log.task.success(task.tplId, `发送给用户 ${subscriber.openId}: 成功 (第${newCount}次推送)`, {
        openId: subscriber.openId,
        pushCount: newCount,
        receivedAt: formatChinaTime(updateTime),
        taskType: 'recurring'
      })
    } else {
      log.task.error(task.tplId, `发送给用户 ${subscriber.openId}: 失败`, {
        openId: subscriber.openId,
        errorCode: result.err_no,
        errorMessage: result.err_tips,
        taskType: 'recurring'
      })
    }
  }
  return result
}

export interface MessageTaskPayload {
  task: ScheduledMessageTask
  subscriber: {
    id: string
    openId: string
    count: number
  }
  taskRecord,
  accessToken:string
  retryCount?: number
}

let messageProducer: Producer | null = null

export function setMessageProducer(producer: Producer) {
  messageProducer = producer
}

export async function sendMessageToKafka(payload: MessageTaskPayload) {
  if (!messageProducer) {
    throw new Error('Kafka消息生产者未初始化')
  }

  try {
    await messageProducer.send({
      topic: topics.SUBSCRIBE_MESSAGES,
      messages: [
        {
          value: JSON.stringify(payload)
        }
      ]
    })
    schedulerLogger.debug('消息发送任务已提交到Kafka', {
      tplId: payload.task.tplId,
      openId: payload.subscriber.openId
    })
    return true
  } catch (error) {
    schedulerLogger.error('提交消息任务到Kafka失败', {
      error: error.message,
      tplId: payload.task.tplId
    })
    return false
  }
}
const MAX_RETRIES = 3

export async function startMessageConsumer(consumer: Consumer) { 
  await consumer.subscribe({ topic: topics.SUBSCRIBE_MESSAGES })

  await consumer.run({
    eachMessage: async ({ message }) => {
      try {
        const payload = JSON.parse(message.value?.toString() || '{}') as MessageTaskPayload
        const { task, subscriber,taskRecord,accessToken, retryCount = 0 } = payload

        try {
          await sendDirectMessage(task, subscriber,taskRecord,accessToken)
        } catch (error) {
          if (retryCount < MAX_RETRIES) {
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))
            await sendMessageToKafka({
              ...payload,
              retryCount: retryCount + 1
            })
          } else {
            schedulerLogger.error('消息发送重试次数已达上限', {
              tplId: task.tplId,
              openId: subscriber.openId,
              error: error.message
            })
          }
        }
      } catch (error) {
        schedulerLogger.error('处理Kafka消息失败', {
          error: error.message
        })
      }
    }
  })
}

