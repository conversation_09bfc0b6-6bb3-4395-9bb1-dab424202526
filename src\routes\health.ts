import { Hono } from 'hono'
import { getDb } from '@/db'
import { getRedis } from '@/redis'

const health = new Hono()

// 健康检查接口
health.get('/health', async (c) => {
  try {
    const healthStatus = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: 'unknown',
        redis: 'unknown',
      },
    }

    // 检查数据库连接
    try {
      const db = await getDb()
      if (db) {
        // 简单的数据库查询测试
        await db.execute('SELECT 1')
        healthStatus.checks.database = 'ok'
      }
      else {
        healthStatus.checks.database = 'error'
      }
    }
    catch (error) {
      console.error('Database health check failed:', error)
      healthStatus.checks.database = 'error'
    }

    // 检查 Redis 连接
    try {
      const redis = await getRedis()
      await redis.ping()
      healthStatus.checks.redis = 'ok'
    }
    catch (error) {
      console.error('Redis health check failed:', error)
      healthStatus.checks.redis = 'error'
    }

    // 如果任何检查失败，返回 503
    const hasErrors = Object.values(healthStatus.checks).includes('error')

    if (hasErrors) {
      healthStatus.status = 'error'
      return c.json(healthStatus, 503)
    }

    return c.json(healthStatus, 200)
  }
  catch (error) {
    console.error('Health check error:', error)
    return c.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Internal server error',
    }, 500)
  }
})

// 简单的存活检查（不检查依赖）
health.get('/ping', async (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'pong',
  })
})

// 就绪检查（检查所有依赖）
health.get('/ready', async (c) => {
  try {
    // 检查数据库
    const db = await getDb()
    await db.execute('SELECT 1')

    // 检查 Redis
    const redis = await getRedis()
    await redis.ping()

    return c.json({
      status: 'ready',
      timestamp: new Date().toISOString(),
    })
  }
  catch (error) {
    console.error('Readiness check failed:', error)
    return c.json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error.message,
    }, 503)
  }
})

export default health
