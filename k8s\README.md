# Kubernetes 部署指南

## 概述

本项目使用 Kubernetes 原生功能进行部署，包括：
- **Deployment**: 管理 3 个应用实例
- **Service**: 提供负载均衡
- **Ingress**: 处理 HTTPS 和外部访问

## 前置要求

1. **Kubernetes 集群**: 确保有可用的 K8s 集群
2. **kubectl**: 已配置并能连接到集群
3. **Ingress Controller**: 集群中已安装 Nginx Ingress Controller
4. **SSL 证书**: `certs/server.pem` 和 `certs/server.key`

## 快速部署

### 1. 自动部署（推荐）

```bash
cd k8s
chmod +x deploy.sh
./deploy.sh
```

### 2. 手动部署

```bash
# 1. 创建 SSL Secret
kubectl create secret tls nqhy-tls-secret \
  --cert=../certs/server.pem \
  --key=../certs/server.key

# 2. 部署应用
kubectl apply -f deployment.yaml

# 3. 部署 Ingress
kubectl apply -f ingress.yaml

# 4. 检查状态
kubectl get all -l app=nqhy-backend
```

## 配置说明

### Deployment 配置
- **副本数**: 3 个实例
- **镜像**: `nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:latest`
- **端口**: 3000
- **资源限制**: 512Mi 内存, 500m CPU

### Service 配置
- **类型**: LoadBalancer
- **端口**: 3000

### Ingress 配置
- **HTTPS**: 自动 SSL 终止
- **负载均衡**: K8s 原生负载均衡
- **域名**: localhost（需要替换为实际域名）

## 访问应用

### 通过 Ingress（生产环境）
```bash
# 获取 Ingress IP
kubectl get ingress nqhy-backend-ingress

# 访问应用（替换为实际 IP 或域名）
https://your-domain.com
```

### 通过端口转发（开发/测试）
```bash
# 转发到本地端口
kubectl port-forward svc/nqhy-backend 3000:3000

# 访问应用
https://localhost:3000
```

## 常用命令

### 查看状态
```bash
# 查看所有资源
kubectl get all -l app=nqhy-backend

# 查看 Pod 详情
kubectl describe pods -l app=nqhy-backend

# 查看 Ingress 状态
kubectl get ingress nqhy-backend-ingress
```

### 查看日志
```bash
# 查看所有 Pod 日志
kubectl logs -l app=nqhy-backend -f

# 查看特定 Pod 日志
kubectl logs <pod-name> -f
```

### 扩缩容
```bash
# 扩展到 5 个实例
kubectl scale deployment nqhy-backend --replicas=5

# 缩减到 2 个实例
kubectl scale deployment nqhy-backend --replicas=2
```

### 更新应用
```bash
# 更新镜像
kubectl set image deployment/nqhy-backend app=nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:v1.1.0

# 查看滚动更新状态
kubectl rollout status deployment/nqhy-backend

# 回滚到上一个版本
kubectl rollout undo deployment/nqhy-backend
```

## 故障排除

### 1. Pod 无法启动
```bash
# 查看 Pod 事件
kubectl describe pod <pod-name>

# 查看 Pod 日志
kubectl logs <pod-name>
```

### 2. Ingress 无法访问
```bash
# 检查 Ingress Controller
kubectl get pods -n ingress-nginx

# 检查 Ingress 配置
kubectl describe ingress nqhy-backend-ingress
```

### 3. SSL 证书问题
```bash
# 检查 Secret
kubectl describe secret nqhy-tls-secret

# 重新创建 Secret
kubectl delete secret nqhy-tls-secret
kubectl create secret tls nqhy-tls-secret --cert=../certs/server.pem --key=../certs/server.key
```

## 清理资源

```bash
# 删除所有相关资源
kubectl delete -f deployment.yaml
kubectl delete -f ingress.yaml
kubectl delete secret nqhy-tls-secret
```
