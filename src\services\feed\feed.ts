
import { getDb } from '@/db'
import { getAccessToken } from '@/redis'
import { nqhyActiveUser, xthyLoginData, xthyUserData } from '@/schema'
import crypto from 'crypto'
import z from 'zod'
import { and, eq, exists, gte, lte } from "drizzle-orm"
import { getChinaTime, getDaysDifference, isPreviousDate, isToday } from '@/utils/time'
import { shouldExecuteTask, shouldExecuteTaskAfter } from '@/common'
import { log, schedulerLogger } from '@/lib/logger'
import dayjs from 'dayjs'
// 响应场景 schema 定义
const SceneSchema = z.object({
    scene: z.number().max(3).min(1)
        .describe('1:离线收益 2:体力恢复 3:重要事件掉落'),
    content_ids: z.array(z.string()),
    extra: z.string().max(100).optional()
        .describe('额外信息，长度<100字符')
})

// 请求参数 schema 定义
const FeedRequestParamsSchema = z.object({
    nonce: z.string()
        .describe('签名用的随机字符串，10位'),
    timestamp: z.string()
        .describe('秒级时间戳'),
    openid: z.string()
        .describe('用户openid'),
    appid: z.string()
        .describe('小游戏的appid')
})

// 响应数据 schema 定义
const FeedResponseSchema = z.object({
    err_no: z.number(),
    err_msg: z.string(),
    data: z.object({
        scenes: z.array(SceneSchema)
    })
})

export async function feedSubscribe(c:any) {
    try{
        const reqSignature = c.req.header('x-signature')
        const nonce =  c.req.query("nonce")
        const timestamp =  c.req.query("timestamp")
        const openid =  c.req.query("openid")
        const appid =  c.req.query("appid")
        const body= {
            nonce,
            timestamp,
            openid,
            appid
        }
        const parse = FeedRequestParamsSchema.safeParse(body)
        if (!parse.success) {
            log.error(`直出抖音参数验证失败`,body)
            return new Response(JSON.stringify(FeedResponseSchema.parse({
                err_no: 28001007,
                err_msg: "invalid param",
                data: {
                    scenes:[]
                }
            })), {
                status:200,
                headers: {
                    'content-type': 'application/json',
                },
              })
        }
        const params = parse.data
        const secret = "EzW12593207298OtD"
        const count = 4
        const calculatedSignature = getDouyinFeedSignature(params, "", secret);
        if (!reqSignature || (reqSignature !== calculatedSignature)) {
            log.error(params.openid,`直出抖音签名验证失败`)
            return new Response(JSON.stringify(FeedResponseSchema.parse({
                err_no: 28006009,
                err_msg: "check signature failed",
                data: {
                    scenes:[]
                }
            })),
            {
                status:200,
                headers: {
                    'content-type': 'application/json',
                    'x-signature': calculatedSignature
                },
            })
        }
        const db = await getDb()
        if (!db) {
          throw new Error('数据库未初始化')
        }
        
        const loginRecord = await db
        .select({
          openId: xthyUserData.openId,
          appId: xthyUserData.appId, 
          update_time: xthyUserData.updateTime
        })
        .from(xthyUserData)
        .where(and(
          eq(xthyUserData.openId, params.openid),
          eq(xthyUserData.appId, params.appid),
          exists(
            db.select().from(nqhyActiveUser)
              .where(and(eq(nqhyActiveUser.openId, params.openid),eq(nqhyActiveUser.eventCode,`stage_end_stage_${count}`)))
          )
        ))
        .execute()
         const config = [
            {
                //盲盒
                tplId:"CONTENT12545435650",
                cronExpression:'* 10 * * *',
                appId:"tt5e4928f4c142201d02",
                meta: {
                    scene: 2,
                    extra: ""
                }
            },
            {
                //奇遇
                tplId:"CONTENT12545435906",
                cronExpression:'* 14 * * *',
                appId:"tt5e4928f4c142201d02",
                meta: {
                    scene: 2,
                    extra: ""
                }
            },
        ]
        const device = loginRecord.every(r=>{
            return isPreviousDate(r.update_time) && getDaysDifference(getChinaTime(), r.update_time) < 3
        })
        if(!device || !loginRecord.length){
            // log.info(params.openid,`用户有今日后之后登录记录或沒有登錄->`+" openid:"+params.openid)
            return formatDouyinFeedRespone(200,params,secret,FeedResponseSchema.parse({
                err_no: 0,
                err_msg: "",
                data: {
                    scenes:[]
                }
            }))
        }
        const content_ids = []
        config.forEach(c=>{
            if(shouldExecuteTaskAfter(c.cronExpression)&&c.appId===params.appid){
                content_ids.push(c.tplId)
            }
        })
    
        const responseBody = FeedResponseSchema.parse({
            err_no: 0,
            err_msg: "",
            data: {
                scenes:[{
                    scene: 2,
                    content_ids: content_ids,
                    extra: "" 
                }]   
            }
        });
        log.info(`直出抖音成功->`+ content_ids.toString()+" openid:"+params.openid)
        return formatDouyinFeedRespone(200,params,secret,responseBody)
    }catch(e){
        console.log(e)
        log.error(`直出抖音执行异常`)
        return new Response(JSON.stringify(FeedResponseSchema.parse({
            err_no: 0,
            err_msg: "",
            data: {
                scenes:[]
            }
        })), {
            status:200,
            headers: {
                'content-type': 'application/json',
            },
          })
    }
    
}

const formatDouyinFeedRespone= (status:number,params:z.infer<typeof FeedRequestParamsSchema>,secret:string,responseBody:z.infer<typeof FeedResponseSchema>) => {
    const responseSignature = getDouyinFeedSignature(params, JSON.stringify(responseBody), secret);

    return new Response(JSON.stringify(responseBody), {
        status,
        headers: {
            'content-type': 'application/json',
            'x-signature': responseSignature
        },
      })
}
/*
    参数示例，params对应url里的query参数
    const params = {
        "nonce":     "1234567890",
        "timestamp": "1717038098",
        "openid":    "ab-cdefghijk",
        "appid":     "tt123456789",
    }
    const secret = "secret"
*/
// 请求头签名计算时bodyStr为空字符串， 响应头签名计算时bodyStr为对应响应体字符串
const getDouyinFeedSignature = (params:z.infer<typeof FeedRequestParamsSchema>, bodyStr:string, secret:string) => { 
    const keys = Object.keys(params).sort();
    const kvList = keys.map(key => `${key}=${params[key]}`);
    const urlParams = kvList.join('&');
    const rawData = urlParams + bodyStr + secret;
    const md5sum = crypto.createHash('md5');
    md5sum.update(rawData);
    const md5Result = md5sum.digest();
    return Buffer.from(md5Result).toString('base64');
}