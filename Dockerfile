# 使用官方Node.js镜像作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV TZ=Asia/Shanghai

# 安装系统依赖
RUN apk add --no-cache \
    tzdata \
    curl \
    openssh-client \
    netcat-openbsd \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

# 复制package文件
COPY package*.json ./
COPY pnpm-lock.yaml* ./

# 安装pnpm、tsx、typescript和tsconfig-paths
RUN npm install -g pnpm tsx typescript tsconfig-paths

# 安装依赖
RUN pnpm install --no-frozen-lockfile

# 复制源代码
COPY . .

# 构建项目（如果构建失败，将使用tsx直接运行）
RUN pnpm run build || echo "Build failed, will use tsx to run TypeScript directly"

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S appuser -u 1001 -G nodejs

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R appuser:nodejs /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 3000

# 健康检查
# HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
#   CMD curl -f http://localhost:3000/health || exit 1

# 启动命令 - 使用启动脚本
CMD ["sh", "-c", "node src/ssh-tunnel.js & if [ -f dist/index.js ]; then node dist/index.js; else tsx src/index.ts; fi"]