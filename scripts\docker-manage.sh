#!/bin/bash

# Docker Compose 管理脚本
# 用于管理 nqhy-backend 项目的 Docker 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="nqhy-backend"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Docker Compose 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动所有服务"
    echo "  stop      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  build     - 构建应用镜像"
    echo "  rebuild   - 重新构建并启动"
    echo "  logs      - 查看日志"
    echo "  status    - 查看服务状态"
    echo "  clean     - 清理未使用的资源"
    echo "  reset     - 重置所有数据（危险操作）"
    echo "  test      - 测试连接"
    echo "  help      - 显示此帮助信息"
    echo ""
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_message $RED "错误: Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
}

# 获取 docker-compose 命令
get_compose_cmd() {
    if command -v docker-compose &> /dev/null; then
        echo "docker-compose"
    else
        echo "docker compose"
    fi
}

# 启动服务
start_services() {
    print_message $BLUE "🚀 启动 $PROJECT_NAME 服务..."
    
    local compose_cmd=$(get_compose_cmd)
    
    # 先启动基础服务
    print_message $YELLOW "启动基础服务 (Redis, Zookeeper, Kafka)..."
    $compose_cmd up -d redis zookeeper kafka
    
    # 等待基础服务就绪
    print_message $YELLOW "等待基础服务就绪..."
    sleep 10
    
    # 启动应用服务
    print_message $YELLOW "启动应用服务..."
    $compose_cmd up -d app
    
    print_message $GREEN "✅ 所有服务已启动"
    show_status
}

# 停止服务
stop_services() {
    print_message $BLUE "🛑 停止 $PROJECT_NAME 服务..."
    
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd down
    
    print_message $GREEN "✅ 所有服务已停止"
}

# 重启服务
restart_services() {
    print_message $BLUE "🔄 重启 $PROJECT_NAME 服务..."
    stop_services
    start_services
}

# 构建镜像
build_image() {
    print_message $BLUE "🔨 构建 $PROJECT_NAME 镜像..."
    
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd build app
    
    print_message $GREEN "✅ 镜像构建完成"
}

# 重新构建并启动
rebuild_and_start() {
    print_message $BLUE "🔨 重新构建并启动 $PROJECT_NAME..."
    
    local compose_cmd=$(get_compose_cmd)
    
    # 停止服务
    $compose_cmd down
    
    # 构建镜像
    $compose_cmd build app
    
    # 启动服务
    start_services
}

# 查看日志
show_logs() {
    print_message $BLUE "📋 查看 $PROJECT_NAME 日志..."
    
    local compose_cmd=$(get_compose_cmd)
    
    if [ $# -eq 0 ]; then
        # 显示所有服务的日志
        $compose_cmd logs -f
    else
        # 显示指定服务的日志
        $compose_cmd logs -f "$1"
    fi
}

# 查看服务状态
show_status() {
    print_message $BLUE "📊 $PROJECT_NAME 服务状态:"
    
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd ps
    
    echo ""
    print_message $BLUE "🌐 服务端点:"
    echo "  应用服务: http://localhost:3000"
    echo "  Redis: localhost:6379"
    echo "  Kafka: localhost:9092"
}

# 清理资源
clean_resources() {
    print_message $BLUE "🧹 清理未使用的 Docker 资源..."
    
    docker system prune -f
    docker volume prune -f
    
    print_message $GREEN "✅ 清理完成"
}

# 重置所有数据
reset_all() {
    print_message $RED "⚠️  警告: 这将删除所有数据和容器!"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message $BLUE "🗑️  重置 $PROJECT_NAME..."
        
        local compose_cmd=$(get_compose_cmd)
        
        # 停止并删除所有容器、网络、卷
        $compose_cmd down -v --remove-orphans
        
        # 删除相关镜像
        docker rmi $(docker images "${PROJECT_NAME}*" -q) 2>/dev/null || true
        
        print_message $GREEN "✅ 重置完成"
    else
        print_message $YELLOW "操作已取消"
    fi
}

# 测试连接
test_connections() {
    print_message $BLUE "🧪 测试服务连接..."
    
    local compose_cmd=$(get_compose_cmd)
    
    # 检查服务是否运行
    if ! $compose_cmd ps | grep -q "Up"; then
        print_message $RED "错误: 服务未运行，请先启动服务"
        exit 1
    fi
    
    # 在应用容器中运行连接测试
    $compose_cmd exec app npm run test:connections
}

# 主函数
main() {
    check_requirements
    
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        build)
            build_image
            ;;
        rebuild)
            rebuild_and_start
            ;;
        logs)
            show_logs "${2:-}"
            ;;
        status)
            show_status
            ;;
        clean)
            clean_resources
            ;;
        reset)
            reset_all
            ;;
        test)
            test_connections
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "错误: 未知命令 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
