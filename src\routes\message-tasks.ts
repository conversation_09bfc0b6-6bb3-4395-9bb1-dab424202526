import { createRoute, z } from '@hono/zod-openapi'
import app from '../app'
import { messageTasks, triggerMessageTask } from '../services/scheduler'

// 手动触发消息发送的API
const triggerMessageRoute = createRoute({
  method: 'post',
  path: '/trigger-message',
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            tplId: z.string().min(1),
            customData: z.record(z.string(), z.object({
              value: z.string(),
            })).optional(),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: '触发成功',
    },
    400: {
      content: {
        'application/json': {
          schema: z.object({
            error: z.string(),
          }),
        },
      },
      description: '参数错误',
    },
    500: {
      content: {
        'application/json': {
          schema: z.object({
            error: z.string(),
          }),
        },
      },
      description: '服务器错误',
    },
  },
})

// 获取任务配置列表的API
const getTasksRoute = createRoute({
  method: 'get',
  path: '/message-tasks',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            tasks: z.array(z.object({
              tplId: z.string(),
              cronExpression: z.string(),
              enabled: z.boolean(),
              page: z.string().optional(),
            })),
          }),
        },
      },
      description: '获取任务列表成功',
    },
  },
})

app.openapi(triggerMessageRoute, async (c) => {
  try {
    const { tplId, customData } = c.req.valid('json')

    await triggerMessageTask(tplId, customData)

    return c.json({
      success: true,
      message: `消息发送任务 ${tplId} 已触发`,
    }, 200)
  }
  catch (error) {
    console.error('触发消息发送失败:', error)
    return c.json({
      error: error instanceof Error ? error.message : '未知错误',
    }, 500)
  }
})

app.openapi(getTasksRoute, async (c) => {
  const tasks = messageTasks.map(task => ({
    tplId: task.tplId,
    cronExpression: task.cronExpression,
    enabled: task.enabled,
    page: task.page,
  }))

  return c.json({ tasks }, 200)
})
