2025/08/11 09:26:04 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:05 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:06 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:06 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:08 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:10 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:13 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:20 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:33 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:26:59 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:27:51 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:44
2025/08/11 09:28:51 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:28:51 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:125
2025/08/11 09:29:52 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:29:52 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:125
2025/08/11 09:30:52 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:30:52 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:125
2025/08/11 09:31:52 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:31:52 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:125
2025/08/11 09:32:53 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:32:53 [emerg] 1#1: "proxy_timeout" directive is not allowed here in /etc/nginx/conf.d/default.conf:125
2025/08/11 09:33:53 [warn] 1#1: the "listen ... http2" directive is deprecated, use the "http2" directive instead in /etc/nginx/conf.d/default.conf:87
2025/08/11 09:33:53 [warn] 1#1: conflicting server name "localhost" on 0.0.0.0:80, ignored
