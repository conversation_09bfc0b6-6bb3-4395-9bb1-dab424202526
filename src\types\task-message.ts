/**
 * 定时任务消息类型定义
 */

// 任务类型枚举
export enum TaskType {
  DOUYIN_MESSAGE = 'douyin_message',
  DATA_SYNC = 'data_sync',
  CLEANUP = 'cleanup',
  REPORT = 'report',
}

// 基础任务消息接口
export interface BaseTaskMessage {
  taskType: TaskType
  taskId: string
  scheduledTime: string
  createdAt: string
  retryCount?: number
  maxRetries?: number
  priority?: number
}
export interface BaseTaskResult {
  totalUsers: number
  message: string
  results: {
    openId: string
    success: boolean
    error?: string
  }[]
}

// 抖音消息任务参数
export interface DouyinMessageTaskParams {
  tplId: string
  messageData: Record<string, string>[]
  enabled: boolean
  cronExpression: string
}

// 抖音消息任务消息
export interface DouyinMessageTask extends BaseTaskMessage {
  taskType: TaskType.DOUYIN_MESSAGE
  parameters: DouyinMessageTaskParams
}

// 数据同步任务参数
export interface DataSyncTaskParams {
  syncType: 'user' | 'order' | 'product'
  batchSize?: number
  lastSyncTime?: string
}

// 数据同步任务消息
export interface DataSyncTask extends BaseTaskMessage {
  taskType: TaskType.DATA_SYNC
  parameters: DataSyncTaskParams
}

// 清理任务参数
export interface CleanupTaskParams {
  targetType: 'logs' | 'cache' | 'temp_files'
  olderThan: string // ISO date string
  dryRun?: boolean
}

// 清理任务消息
export interface CleanupTask extends BaseTaskMessage {
  taskType: TaskType.CLEANUP
  parameters: CleanupTaskParams
}

// 报告任务参数
export interface ReportTaskParams {
  reportType: 'daily' | 'weekly' | 'monthly'
  recipients: string[]
  includeCharts?: boolean
}

// 报告任务消息
export interface ReportTask extends BaseTaskMessage {
  taskType: TaskType.REPORT
  parameters: ReportTaskParams
}

// 联合类型：所有任务消息
export type TaskMessage
  = | DouyinMessageTask
    | DataSyncTask
    | CleanupTask
    | ReportTask

// 任务执行结果
export interface TaskExecutionResult {
  taskId: string
  taskType: TaskType
  success: boolean
  startTime: string
  endTime: string
  duration: number
  result?: any
  error?: string
  retryCount: number
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRYING = 'retrying',
  CANCELLED = 'cancelled',
}

// 任务执行上下文
export interface TaskExecutionContext {
  taskMessage: TaskMessage
  startTime: Date
  logger: any
  retryCount: number
  isRetry: boolean
}

// 任务处理器接口
export interface TaskHandler<T extends TaskMessage = TaskMessage> {
  taskType: TaskType
  handle: (context: TaskExecutionContext) => Promise<TaskExecutionResult>
  validateParams?: (params: T['parameters']) => boolean
  getMaxRetries?: () => number
  getRetryDelay?: (retryCount: number) => number
}

// 任务调度配置
export interface TaskScheduleConfig {
  taskType: TaskType
  cronExpression: string
  enabled: boolean
  maxRetries: number
  priority: number
  parameters: any
}

// 任务统计信息
export interface TaskStats {
  taskType: TaskType
  totalExecuted: number
  successCount: number
  failureCount: number
  averageDuration: number
  lastExecutionTime?: string
}
