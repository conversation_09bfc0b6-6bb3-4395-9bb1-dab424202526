{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "types": ["node"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": false, "outDir": "./dist", "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "baseUrl": "."}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}