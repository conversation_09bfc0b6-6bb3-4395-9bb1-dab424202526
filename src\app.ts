import { cors } from 'hono/cors'
import createApp from '@/lib/create-app'
import health from './routes/health'
import { sendBatchCards } from './services/card/sendCard'
import { feedSubscribe } from './services/feed/feed'
import { sendFeedCache } from './services/feed/feedCache'
import { checkAndExecuteScheduledTasks } from './services/scheduler'
import { execSubscribe, handleSubscribe } from './subscribe'
import 'dotenv/config'

const app = createApp()
app.use('/posts/*', cors())

// 健康检查路由
app.route('/', health)

// sendBatchCards({
//   tplId: '30888',
//   cronExpression: '* 16 * * *',
//   appId: 'tt5e4928f4c142201d02',
//   messageData: [10, 1, 10, 10, 20, 1, 1],
//   meta: {
//     scene: 1,
//     extra: '',
//   },
//   type: 'feed',
// })
app.get('/feedSubscribe', feedSubscribe)
app.post('/subscribeMessage', handleSubscribe)
app.post('/subscribeMessageSingle', execSubscribe)
app.post('/check', checkAndExecuteScheduledTasks)

// 根路径处理
app.get('/', (c) => {
  return c.json({
    name: 'NQHY Backend API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
  })
})

// 404处理
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: `路径 ${c.req.path} 不存在`,
    timestamp: new Date().toISOString(),
  }, 404)
})

export default app
