# yaml-language-server: $schema=https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json
services:
  # App 实例 1
  app1:
    # 使用远程镜像仓库
    image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:${IMAGE_TAG:-latest}
    container_name: nqhy-backend-1
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile

    # 镜像拉取策略
    pull_policy: always
    ports:
      - "12001:3000"
    environment:
      # 基础配置
      NODE_ENV: production
      PORT: 3000
      ENVIRONMENT: production
      TZ: Asia/Shanghai

      # HTTPS配置 - 由Nginx处理SSL终止，应用使用HTTP
      HTTPS_ENABLED: false

      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json

      # 数据库配置（通过SSH隧道连接）
      DATABASE_URL: mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007

      # Redis配置
      REDIS_PASSWORD: cnmb@Gunsb!
      REDIS_HOST: 127.0.0.1
      REDIS_PORT: 6379

      # # SSH隧道配置
      # SSH_HOST: *************
      # SSH_PORT: 22
      # SSH_USER: root
      # SSH_PASS: ${SSH_PASSWORD} # 从环境变量读取

      # Kafka配置
      KAFKA_ENABLED: false
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      KAFKA_USERNAME: admin
      KAFKA_PASSWORD: 12345678
      KAFKA_GROUP_ID: nqhy-backend-group

      # 定时任务配置 direct kafka
      SCHEDULER_ENABLED: true
      SCHEDULER_MAIN_INSTANCE: true
      SCHEDULER_MODE: direct
      INSTANCE_ID: instance-1

    volumes:
      # 日志持久化
      - ./logs:/app/logs
      # SSH密钥（如果使用密钥认证）
      # - ~/.ssh:/home/<USER>/.ssh:ro

    networks:
      - nqhy-network

    # depends_on:
    #   - kafka

    # 健康检查
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 30s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # App 实例 2
  app2:
    # 使用远程镜像仓库
    image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:${IMAGE_TAG:-latest}
    container_name: nqhy-backend-2
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile

    # 镜像拉取策略
    pull_policy: always
    ports:
      - "12002:3000"
    environment:
      # 基础配置
      NODE_ENV: production
      PORT: 3000
      ENVIRONMENT: production
      TZ: Asia/Shanghai

      # HTTPS配置 - 由Nginx处理SSL终止，应用使用HTTP
      HTTPS_ENABLED: false

      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json

      # 数据库配置（通过SSH隧道连接）
      DATABASE_URL: mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007

      # Redis配置
      REDIS_PASSWORD: cnmb@Gunsb!
      REDIS_HOST: 127.0.0.1
      REDIS_PORT: 6379

      # Kafka配置
      KAFKA_ENABLED: false
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      KAFKA_USERNAME: admin
      KAFKA_PASSWORD: 12345678
      KAFKA_GROUP_ID: nqhy-backend-group

      # 定时任务配置 - 实例2不运行定时任务
      SCHEDULER_ENABLED: false
      SCHEDULER_MAIN_INSTANCE: false

    networks:
      - nqhy-network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # App 实例 3
  app3:
    # 使用远程镜像仓库
    image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:${IMAGE_TAG:-latest}
    container_name: nqhy-backend-3
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile

    # 镜像拉取策略
    pull_policy: always
    ports:
      - "12003:3000"
    environment:
      # 基础配置
      NODE_ENV: production
      PORT: 3000
      ENVIRONMENT: production
      TZ: Asia/Shanghai

      # HTTPS配置 - 由Nginx处理SSL终止，应用使用HTTP
      HTTPS_ENABLED: false

      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json

      # 数据库配置（通过SSH隧道连接）
      DATABASE_URL: mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007

      # Redis配置
      REDIS_PASSWORD: cnmb@Gunsb!
      REDIS_HOST: 127.0.0.1
      REDIS_PORT: 6379

      # Kafka配置
      KAFKA_ENABLED: false
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      KAFKA_USERNAME: admin
      KAFKA_PASSWORD: 12345678
      KAFKA_GROUP_ID: nqhy-backend-group

      # 定时任务配置 - 实例3不运行定时任务
      SCHEDULER_ENABLED: false
      SCHEDULER_MAIN_INSTANCE: false

    networks:
      - nqhy-network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # Nginx 反向代理服务
  nginx:
    image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nginx:latest
    container_name: nqhy-nginx
    restart: unless-stopped
    ports:
      - "8443:443" # 改为 8443 端口避免冲突
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./certs:/etc/nginx/ssl:roz
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app1
      - app2
      - app3
    networks:
      - nqhy-network
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: "0.25"
        reservations:
          memory: 64M
          cpus: "0.1"

  # Zookeeper 服务
  # zookeeper:
  #   image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/zookeeper:latest
  #   container_name: nqhy-zookeeper
  #   restart: unless-stopped
  #   ports:
  #     - "9996:2181"
  #   healthcheck:
  #     test: ["CMD-SHELL", "echo srvr | nc localhost 2181 || exit 1"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #   environment:
  #     ZOOKEEPER_CLIENT_PORT: 2181
  #     ZOOKEEPER_TICK_TIME: 2000
  #     ZOOKEEPER_INIT_LIMIT: 5
  #     ZOOKEEPER_SYNC_LIMIT: 2
  #   volumes:
  #     - zookeeper_data:/var/lib/zookeeper/data
  #     - zookeeper_logs:/var/lib/zookeeper/log
  #   networks:
  #     - nqhy-network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 256M
  #         cpus: "0.25"

  # # Kafka 服务
  # kafka:
  #   image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/kafka:latest
  #   container_name: nqhy-kafka
  #   restart: unless-stopped
  #   depends_on:
  #     zookeeper:
  #       condition: service_healthy
  #   ports:
  #     - "9998:9092"
  #     - "9995:9101"
  #   environment:
  #     KAFKA_BROKER_ID: 1
  #     KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
  #     KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: 60000
  #     KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
  #     KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:9998
  #     KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092,PLAINTEXT_HOST://0.0.0.0:9998
  #     KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
  #     KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  #     KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
  #     KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
  #     KAFKA_JMX_PORT: 9101
  #     KAFKA_JMX_HOSTNAME: localhost
  #     # SASL 认证配置
  #     KAFKA_SASL_ENABLED_MECHANISMS: PLAIN
  #     KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
  #     KAFKA_LISTENER_NAME_PLAINTEXT_SASL_ENABLED_MECHANISMS: PLAIN
  #   volumes:
  #     - kafka_data:/var/lib/kafka/data
  #   networks:
  #     - nqhy-network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 512M
  #         cpus: "0.5"

  # kafdrop:
  #   image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/kafdrop:latest
  #   container_name: nqhy-kafdrop
  #   restart: unless-stopped
  #   ports:
  #     - "9994:9000"
  #   environment:
  #     KAFKA_BROKERCONNECT: "kafka:9092"
  #     JVM_OPTS: "-Xms32M -Xmx64M"
  #     SERVER_SERVLET_CONTEXTPATH: "/"
  #   depends_on:
  #     - kafka
  #   networks:
  #     - nqhy-network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: "128M"
  #         cpus: "0.2"

#   zookeeper_data:
#     driver: local
#   zookeeper_logs:
#     driver: local
#   kafka_data:
#     driver: local

networks:
  nqhy-network:
    driver: bridge
