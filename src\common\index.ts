/**
 * 解析cron表达式并检查是否应该执行
 * 简化版cron解析器，支持基本格式: 分 时 日 月 周
 */
export function shouldExecuteTask(cronExpression: string): boolean {
  const now = new Date()
  const [minute, hour, day, month, weekday] = cronExpression.split(' ')

  const currentMinute = now.getMinutes()
  const currentHour = now.getHours()
  const currentDay = now.getDate()
  const currentMonth = now.getMonth() + 1
  const currentWeekday = now.getDay()

  // 检查分钟
  if (minute !== '*' && Number.parseInt(minute) !== currentMinute)
    return false

  // 检查小时
  if (hour !== '*' && Number.parseInt(hour) !== currentHour)
    return false

  // 检查日期
  if (day !== '*' && Number.parseInt(day) !== currentDay)
    return false

  // 检查月份
  if (month !== '*' && Number.parseInt(month) !== currentMonth)
    return false

  // 检查星期
  if (weekday !== '*' && Number.parseInt(weekday) !== currentWeekday)
    return false

  return true
}

export function shouldExecuteTaskAfter(cronExpression: string): boolean {
  const now = new Date()
  const [minute, hour, day, month, weekday] = cronExpression.split(' ')

  const currentMinute = now.getMinutes()
  const currentHour = now.getHours()
  const currentDay = now.getDate()
  const currentMonth = now.getMonth() + 1
  const currentWeekday = now.getDay()

  // 检查分钟
  if (minute !== '*' && Number.parseInt(minute) > currentMinute)
    return false

  // 检查小时
  if (hour !== '*' && Number.parseInt(hour) > currentHour)
    return false

  // 检查日期
  if (day !== '*' && Number.parseInt(day) !== currentDay)
    return false

  // 检查月份
  if (month !== '*' && Number.parseInt(month) !== currentMonth)
    return false

  // 检查星期
  if (weekday !== '*' && Number.parseInt(weekday) !== currentWeekday)
    return false

  return true
}
