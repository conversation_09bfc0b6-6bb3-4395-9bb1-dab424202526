import type { ConsumerConfig, KafkaConfig, ProducerConfig } from 'kafkajs'

// Kafka连接配置
export const kafkaConfig: KafkaConfig = {
  clientId: 'nqhy-backend',
  brokers: [process.env.KAFKA_BOOTSTRAP_SERVERS || 'kafka:9092'],
  // 在 Docker 环境中暂时禁用 SASL 认证，如需要可以启用
  // sasl: {
  //   mechanism: 'plain',
  //   username: process.env.KAFKA_USERNAME || 'admin',
  //   password: process.env.KAFKA_PASSWORD || '12345678',
  // },
  ssl: false,
  connectionTimeout: 10000,
  authenticationTimeout: 5000,
  reauthenticationThreshold: 10000,
  requestTimeout: 30000,
  enforceRequestTimeout: false,
  retry: {
    initialRetryTime: 100,
    retries: 8,
  },
}

// Producer配置
export const producerConfig: ProducerConfig = {
  maxInFlightRequests: 1,
  idempotent: false,
  transactionTimeout: 30000,
  retry: {
    initialRetryTime: 100,
    retries: 3,
  },
}

// Consumer配置
export const consumerConfig: ConsumerConfig = {
  groupId: process.env.KAFKA_GROUP_ID || 'nqhy-backend-group',
  sessionTimeout: 30000,
  rebalanceTimeout: 60000,
  heartbeatInterval: 3000,
  metadataMaxAge: 300000,
  allowAutoTopicCreation: true,
  maxBytesPerPartition: 1048576,
  minBytes: 1,
  maxBytes: 10485760,
  maxWaitTimeInMs: 5000,
  retry: {
    initialRetryTime: 100,
    retries: 8,
  },
}

// 主题配置
export const topics = {
  SCHEDULED_TASKS: 'scheduled_tasks',
  SUBSCRIBE_MESSAGES: 'subscribe_messages', // 添加新主题
} as const

export type TopicName = typeof topics[keyof typeof topics]
