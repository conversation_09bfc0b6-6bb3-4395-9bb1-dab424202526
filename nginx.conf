upstream nqhy_backend {
    # 主服务器
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    # 备用服务器（部署时使用）
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 80;
    server_name localhost;

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 代理到后端服务
    location / {
        proxy_pass http://nqhy_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 重试设置
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 10s;
    }
}
