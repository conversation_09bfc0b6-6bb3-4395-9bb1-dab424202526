/**
 * 星通互娱用户信息服务
 */

import { getDb } from '@/db'
import { xthyUserData } from '@/schema'
import { eq, and, gte, lte, desc, count, sql } from 'drizzle-orm'
import { logger } from '@/lib/logger'
import { getChinaTime } from '@/utils/time'
import type { 
  NewXthyUserData, 
  XthyUserData, 
  CreateXthyUserRequest,
  UpdateXthyUserRequest,
  XthyUserQueryParams,
  XthyUserStats,
  SignInReward
} from '@/types/xthy-user'

/**
 * 创建用户信息记录
 */
export async function createUser(data: CreateXthyUserRequest): Promise<XthyUserData> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const now = getChinaTime()
  
  const insertData: NewXthyUserData = {
    appId: data.appId,
    openId: data.openId,
    unionId: data.unionId,
    latestAdId: data.latestAdId,
    latestClickId: data.latestClickId,
    platform: data.platform,
    createTime: now,
    updateTime: now,
  }

  const [result] = await db
    .insert(xthyUserData)
    .values(insertData)
    .execute()

  logger.info('创建用户信息记录', { 
    openId: data.openId, 
    insertId: result.insertId 
  })

  // 查询并返回创建的记录
  const [created] = await db
    .select()
    .from(xthyUserData)
    .where(eq(xthyUserData.nonId, Number(result.insertId)))
    .execute()

  return created
}

/**
 * 根据 openId 查询用户信息
 */
export async function getUserByOpenId(openId: string): Promise<XthyUserData | null> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const [result] = await db
    .select()
    .from(xthyUserData)
    .where(eq(xthyUserData.openId, openId))
    .execute()

  return result || null
}

/**
 * 更新用户信息
 */
export async function updateUser(
  nonId: number, 
  data: UpdateXthyUserRequest
): Promise<XthyUserData | null> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const updateData: Partial<NewXthyUserData> = {
    ...data,
    updateTime: getChinaTime(),
  }

  if (data.postBackTime) {
    updateData.postBackTime = new Date(data.postBackTime)
  }

  await db
    .update(xthyUserData)
    .set(updateData)
    .where(eq(xthyUserData.nonId, nonId))
    .execute()

  logger.info('更新用户信息记录', { nonId, ...data })

  // 查询并返回更新后的记录
  const [updated] = await db
    .select()
    .from(xthyUserData)
    .where(eq(xthyUserData.nonId, nonId))
    .execute()

  return updated || null
}

/**
 * 获取签到用户列表（昨天到6天前注册的用户）
 * 返回用户信息和对应的签到天数索引
 */
export async function getSignInUsers(): Promise<SignInReward[]> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const now = getChinaTime()
  
  // 计算昨天和6天前的日期
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  yesterday.setHours(0, 0, 0, 0)
  
  const sixDaysAgo = new Date(now)
  sixDaysAgo.setDate(sixDaysAgo.getDate() - 6)
  sixDaysAgo.setHours(0, 0, 0, 0)
  
  const endOfYesterday = new Date(yesterday)
  endOfYesterday.setHours(23, 59, 59, 999)

  logger.info('查询签到用户', {
    sixDaysAgo: sixDaysAgo.toISOString(),
    endOfYesterday: endOfYesterday.toISOString()
  })

  // 查询昨天到6天前注册的用户
  const users = await db
    .select()
    .from(xthyUserData)
    .where(and(
      gte(xthyUserData.createTime, sixDaysAgo),
      lte(xthyUserData.createTime, endOfYesterday)
    ))
    .orderBy(desc(xthyUserData.createTime))
    .execute()

  // 计算每个用户的签到天数索引
  const signInRewards: SignInReward[] = users.map(user => {
    const createDate = new Date(user.createTime)
    const currentDate = new Date(now)
    
    // 计算天数差值
    const timeDiff = currentDate.getTime() - createDate.getTime()
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    
    // 签到天数索引（1-7天）
    const dayIndex = Math.min(Math.max(daysDiff, 1), 7)

    return {
      openId: user.openId || '',
      dayIndex,
      createTime: user.createTime,
      rewardData: {} // 这里可以根据dayIndex设置对应的奖励数据
    }
  })

  logger.info('获取签到用户完成', {
    totalUsers: signInRewards.length,
    dayIndexDistribution: signInRewards.reduce((acc, reward) => {
      acc[reward.dayIndex] = (acc[reward.dayIndex] || 0) + 1
      return acc
    }, {} as Record<number, number>)
  })

  return signInRewards
}

/**
 * 分页查询用户信息
 */
export async function queryUsers(params: XthyUserQueryParams): Promise<{
  data: XthyUserData[]
  total: number
  page: number
  pageSize: number
}> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const {
    openId,
    unionId,
    appId,
    platform,
    clickIdCallbackFlag,
    advertiserId,
    openGm,
    openFreeAd,
    startTime,
    endTime,
    page = 1,
    pageSize = 20
  } = params

  // 构建查询条件
  const conditions = []
  
  if (openId) conditions.push(eq(xthyUserData.openId, openId))
  if (unionId) conditions.push(eq(xthyUserData.unionId, unionId))
  if (appId) conditions.push(eq(xthyUserData.appId, appId))
  if (platform) conditions.push(eq(xthyUserData.platform, platform))
  if (clickIdCallbackFlag) conditions.push(eq(xthyUserData.clickIdCallbackFlag, clickIdCallbackFlag))
  if (advertiserId) conditions.push(eq(xthyUserData.advertiserId, advertiserId))
  if (openGm !== undefined) conditions.push(eq(xthyUserData.openGm, openGm))
  if (openFreeAd !== undefined) conditions.push(eq(xthyUserData.openFreeAd, openFreeAd))
  if (startTime) conditions.push(gte(xthyUserData.createTime, new Date(startTime)))
  if (endTime) conditions.push(lte(xthyUserData.createTime, new Date(endTime)))

  const whereClause = conditions.length > 0 ? and(...conditions) : undefined

  // 查询总数
  const [{ total }] = await db
    .select({ total: count() })
    .from(xthyUserData)
    .where(whereClause)
    .execute()

  // 查询数据
  const data = await db
    .select()
    .from(xthyUserData)
    .where(whereClause)
    .orderBy(desc(xthyUserData.createTime))
    .limit(pageSize)
    .offset((page - 1) * pageSize)
    .execute()

  return {
    data,
    total,
    page,
    pageSize
  }
}

/**
 * 获取用户统计信息
 */
export async function getUserStats(
  startTime?: Date,
  endTime?: Date
): Promise<XthyUserStats> {
  const db = await getDb()
  if (!db) {
    throw new Error('数据库连接失败')
  }

  const conditions = []
  if (startTime) conditions.push(gte(xthyUserData.createTime, startTime))
  if (endTime) conditions.push(lte(xthyUserData.createTime, endTime))
  const whereClause = conditions.length > 0 ? and(...conditions) : undefined

  // 总用户数
  const [{ totalUsers }] = await db
    .select({ totalUsers: count() })
    .from(xthyUserData)
    .where(whereClause)
    .execute()

  // 回传用户数
  const [{ callbackUsers }] = await db
    .select({ callbackUsers: count() })
    .from(xthyUserData)
    .where(and(
      eq(xthyUserData.clickIdCallbackFlag, 'Y'),
      ...(whereClause ? [whereClause] : [])
    ))
    .execute()

  // GM用户数
  const [{ gmUsers }] = await db
    .select({ gmUsers: count() })
    .from(xthyUserData)
    .where(and(
      eq(xthyUserData.openGm, 1),
      ...(whereClause ? [whereClause] : [])
    ))
    .execute()

  // 免广告用户数
  const [{ freeAdUsers }] = await db
    .select({ freeAdUsers: count() })
    .from(xthyUserData)
    .where(and(
      eq(xthyUserData.openFreeAd, 1),
      ...(whereClause ? [whereClause] : [])
    ))
    .execute()

  // 平台统计
  const platformStats = await db
    .select({
      platform: xthyUserData.platform,
      count: count()
    })
    .from(xthyUserData)
    .where(whereClause)
    .groupBy(xthyUserData.platform)
    .execute()

  // 广告户统计
  const advertiserStats = await db
    .select({
      advertiserId: xthyUserData.advertiserId,
      count: count()
    })
    .from(xthyUserData)
    .where(whereClause)
    .groupBy(xthyUserData.advertiserId)
    .execute()

  return {
    totalUsers,
    callbackUsers,
    callbackRate: totalUsers > 0 ? (callbackUsers / totalUsers) * 100 : 0,
    gmUsers,
    freeAdUsers,
    platformStats: platformStats.map(stat => ({
      platform: stat.platform || 'unknown',
      count: stat.count
    })),
    advertiserStats: advertiserStats.map(stat => ({
      advertiserId: stat.advertiserId || 'unknown',
      count: stat.count
    }))
  }
}
