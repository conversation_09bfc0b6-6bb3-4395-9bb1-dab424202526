import { getDb } from '../db'
import { userSubscribe } from '../schema'
import { eq } from 'drizzle-orm'
import { formatChinaTime, getChinaTime } from '@/utils/time'
import { log } from '@/lib/logger'
import { MessageTaskPayload, MessageResult } from '@/types/message'
import { sendDouyinSubscribeMessage } from './douyin-api'

export async function handleMessageSend(
  payload: MessageTaskPayload
): Promise<MessageResult> {
  const { task, subscriber, taskRecord, accessToken } = payload
  const db = await getDb()
  if (!db) {
    throw new Error('数据库未初始化')
  }

  const result = await sendDouyinSubscribeMessage({
    access_token: accessToken,
    app_id: task.appId,
    open_id: subscriber.openId,
    page: task.page,
    data: task.messageData.at(subscriber.count || 0) as Record<string,string>,
    tpl_id: task.tplId
  })

  // 根据任务类型和发送结果处理
  const metaType = taskRecord.meta && typeof taskRecord.meta === 'object'
    ? (taskRecord.meta as any).type
    : null
  const isSingleTask = metaType === 'single'

  if (isSingleTask) {
    await handleSingleTaskResult(db, task, subscriber, result)
  } else {
    await handleRecurringTaskResult(db, task, subscriber, result)
  }

  return {
    success: result.err_no === 0,
    openId: subscriber.openId,
    err_no: result.err_no,
    err_tips: result.err_tips
  }
}

// ...其他辅助函数实现