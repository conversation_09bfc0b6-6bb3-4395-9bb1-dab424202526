import { messageTasks } from '@/config'
import { shouldExecuteTask } from '@/common'
import { distributedLock } from './distributed-lock'
import { getRedis } from '@/redis'
import { schedulerLogger } from '@/lib/logger'
import { TaskType, DouyinMessageTask, TaskScheduleConfig } from '@/types/task-message'

/**
 * 基于Redis的定时任务调度器
 * 使用Redis List作为任务队列
 */
export class RedisScheduler {
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private scheduleConfigs: TaskScheduleConfig[] = []
  private queueName = 'task_queue'

  constructor() {
    this.initializeScheduleConfigs()
  }

  /**
   * 初始化调度配置
   */
  private initializeScheduleConfigs() {
    this.scheduleConfigs = messageTasks.map(task => ({
      taskType: TaskType.DOUYIN_MESSAGE,
      cronExpression: task.cronExpression,
      enabled: task.enabled,
      maxRetries: 3,
      priority: 1,
      parameters: {
        tplId: task.tplId,
        messageData: task.messageData,
        enabled: task.enabled,
        cronExpression: task.cronExpression,
      },
    }))

    schedulerLogger.info('Redis调度配置初始化完成', {
      configCount: this.scheduleConfigs.length,
      enabledCount: this.scheduleConfigs.filter(c => c.enabled).length,
    })
  }

  /**
   * 启动调度器
   */
  start() {
    if (this.isRunning) {
      schedulerLogger.warn('Redis调度器已在运行中')
      return
    }

    if (process.env.SCHEDULER_ENABLED === 'false') {
      schedulerLogger.info('定时任务调度器已禁用')
      return
    }

    const isMainInstance = process.env.SCHEDULER_MAIN_INSTANCE !== 'false'
    if (!isMainInstance) {
      schedulerLogger.info('当前实例不是主实例，跳过启动定时任务调度器')
      return
    }

    this.isRunning = true
    schedulerLogger.info('启动Redis定时任务调度器', {
      nodeEnv: process.env.NODE_ENV,
      instanceId: process.env.INSTANCE_ID || 'unknown',
      mode: 'redis',
      queueName: this.queueName,
    })

    this.checkAndScheduleTasks()
    this.intervalId = setInterval(() => {
      this.checkAndScheduleTasks()
    }, 60 * 1000)

    this.setupGracefulShutdown()
  }

  /**
   * 停止调度器
   */
  stop() {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    schedulerLogger.info('Redis定时任务调度器已停止')
  }

  /**
   * 检查并调度任务
   */
  private async checkAndScheduleTasks() {
    schedulerLogger.debug('检查定时任务', { timestamp: new Date().toISOString() })

    for (const config of this.scheduleConfigs) {
      if (!config.enabled) {
        continue
      }

      if (shouldExecuteTask(config.cronExpression)) {
        await this.scheduleTask(config)
      }
    }
  }

  /**
   * 调度单个任务（发送到Redis队列）
   */
  private async scheduleTask(config: TaskScheduleConfig) {
    const lockKey = `redis_schedule_${config.taskType}_${config.cronExpression}`

    await distributedLock.executeWithLock(
      lockKey,
      async () => {
        await this.sendTaskToQueue(config)
      },
      300
    )
  }

  /**
   * 发送任务到Redis队列
   */
  private async sendTaskToQueue(config: TaskScheduleConfig) {
    try {
      const redis = await getRedis()
      const now = new Date()
      const taskId = this.generateTaskId(config.taskType, now)

      let taskMessage: any

      switch (config.taskType) {
        case TaskType.DOUYIN_MESSAGE:
          taskMessage = this.createDouyinMessageTask(taskId, config, now)
          break
        default:
          schedulerLogger.warn('未知的任务类型', { taskType: config.taskType })
          return
      }

      // 使用Redis LPUSH将任务推入队列
      await redis.lpush(this.queueName, JSON.stringify(taskMessage))

      schedulerLogger.info('任务消息已发送到Redis队列', {
        taskId,
        taskType: config.taskType,
        scheduledTime: now.toISOString(),
        queueName: this.queueName,
      })

    } catch (error) {
      schedulerLogger.error('发送任务消息到Redis失败', {
        taskType: config.taskType,
        error: error.message,
      })
    }
  }

  /**
   * 创建抖音消息任务
   */
  private createDouyinMessageTask(
    taskId: string,
    config: TaskScheduleConfig,
    scheduledTime: Date
  ): DouyinMessageTask {
    return {
      taskType: TaskType.DOUYIN_MESSAGE,
      taskId,
      scheduledTime: scheduledTime.toISOString(),
      createdAt: new Date().toISOString(),
      retryCount: 0,
      maxRetries: config.maxRetries,
      priority: config.priority,
      parameters: config.parameters,
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(taskType: TaskType, scheduledTime: Date): string {
    const timestamp = scheduledTime.getTime()
    const random = Math.random().toString(36).substring(2, 8)
    return `${taskType}_${timestamp}_${random}`
  }

  /**
   * 手动触发任务
   */
  async triggerTask(taskType: TaskType, parameters: any): Promise<string> {
    const config = this.scheduleConfigs.find(c => c.taskType === taskType)
    if (!config) {
      throw new Error(`未找到任务类型 ${taskType} 的配置`)
    }

    const now = new Date()
    const taskId = this.generateTaskId(taskType, now)

    const customConfig: TaskScheduleConfig = {
      ...config,
      parameters: { ...config.parameters, ...parameters },
    }

    await this.sendTaskToQueue(customConfig)

    schedulerLogger.info('手动触发Redis任务', {
      taskId,
      taskType,
      triggeredAt: now.toISOString(),
    })

    return taskId
  }

  /**
   * 获取队列状态
   */
  async getQueueStatus() {
    try {
      const redis = await getRedis()
      const queueLength = await redis.llen(this.queueName)
      
      return {
        queueName: this.queueName,
        queueLength,
        isRunning: this.isRunning,
      }
    } catch (error) {
      schedulerLogger.error('获取Redis队列状态失败', { error: error.message })
      throw error
    }
  }

  /**
   * 获取调度配置
   */
  getScheduleConfigs(): TaskScheduleConfig[] {
    return [...this.scheduleConfigs]
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown() {
    const shutdown = () => {
      schedulerLogger.info('收到关闭信号，停止Redis调度器')
      this.stop()
    }

    process.on('SIGTERM', shutdown)
    process.on('SIGINT', shutdown)
  }
}

// 导出单例实例
export const redisScheduler = new RedisScheduler()
