{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}}