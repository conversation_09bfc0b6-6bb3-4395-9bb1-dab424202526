import { drizzle } from 'drizzle-orm/mysql2'
import mysql from 'mysql2/promise'
// import * as schema from './schema'

let dbInstance

export async function getDb() {
  if (!dbInstance) {
    const pool = mysql.createPool({
      uri: 'mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007',
      // 连接池优化配置
      connectionLimit: 100, // 最大连接数
      queueLimit: 0, // 队列限制
      waitForConnections: true, // 等待可用连接
      enableKeepAlive: true, // 保持连接活跃
      keepAliveInitialDelay: 0, // 保活延迟
      maxIdle: 10, // 最大空闲连接
      idleTimeout: 60000, // 空闲超时

      // 性能优化
      multipleStatements: true, // 支持多语句
      namedPlaceholders: true, // 命名占位符
    })

    dbInstance = drizzle(pool)
  }

  return dbInstance
}
