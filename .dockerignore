# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist
build
.next

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# IDE文件
.vscode
.idea
*.swp
*.swo

# OS文件
.DS_Store
Thumbs.db

# Git文件
.git
.gitignore

# 测试文件
test
tests
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档文件
README.md
CHANGELOG.md
LICENSE

# Docker文件
Dockerfile
.dockerignore
docker-compose*.yml

# 临时文件
tmp
temp
.tmp

# 开发工具
.eslintrc*
.prettierrc*
jest.config.js

# 脚本文件（根据需要调整）
test-*.js
monitor-*.js
start-*.bat
start-*.ps1
dist
nqhy-backend.tar