import { getDb } from './db';
import dayjs from 'dayjs';
import { sql } from 'drizzle-orm';

// 动态获取事件数据表名
export function getTableName(month: string) {
  if (!/^[0-9]{4}_[0-9]{1,2}$/.test(month)) {
    throw new Error('Invalid month format, expected YYYY_M or YYYY_MM');
  }
  return `xthy_event_data_${month}`;
}

// 获取月份列表
export function getMonthList(startTime: string, endTime: string): string[] {
  const months: string[] = [];
  const start = dayjs(startTime.replace('_', '-'));
  const end = dayjs(endTime.replace('_', '-'));
  let current = start.startOf('month');
  while (current.isBefore(end) || current.isSame(end, 'month')) {
    months.push(current.format('YYYY_M'));
    current = current.add(1, 'month');
  }
  return months;
}

// 查询事件数据（动态表名，分页、可选条件）
export async function queryEventData({
  startTime,
  endTime,
  page = 1,
  limit = 20,
  openId,
  eventCode,
  uuid,
  orderBy = 'event_time',
  order = 'desc',
  userCreateDate,
}: {
  startTime: string;
  endTime: string;
  page?: number;
  limit?: number;
  openId?: string;
  eventCode?: string;
  uuid?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
  userCreateDate?: string;
}) {
  const db = await getDb();
  if (!db) throw new Error('数据库未初始化');
  let eventCodeCondition = '';
  if (eventCode) {
    const codes = Array.isArray(eventCode) ? eventCode : eventCode.split(',');
    if (codes.length > 0) {
      const conditions = codes.map(code => {
        const safeCode = String(code).replace(/'/g, "''");
        return `(event_code = '${safeCode}' OR event_code LIKE '${safeCode}%')`;
      });
      eventCodeCondition = ` AND (${conditions.join(' OR ')})`;
    }
  }
  let months = getMonthList(startTime, endTime);
  if (!months.length) months = [startTime];
  const appIdCondition = ` AND app_id = 'tt5e4928f4c142201d02'`;
  const userCreateDateCondition = userCreateDate
    ? ` AND u.create_time >= '${userCreateDate} 00:00:00' AND u.create_time < '${dayjs(userCreateDate).add(1, 'day').format('YYYY-MM-DD')} 00:00:00'`
    : '';
  const selects = months.map(month => {
    const table = `xthy_event_data_${month}`;
    return `SELECT * FROM \`${table}\` WHERE 1=1${eventCodeCondition}${appIdCondition}`;
  });
  const unionSql = selects.join(' UNION ALL ');
  const sqlStr = `
    SELECT e.*, u.create_time as user_create_time
    FROM (${unionSql}) AS e
    LEFT JOIN xthy_user_data u ON e.open_id = u.open_id
    WHERE 1=1${userCreateDateCondition}
    ORDER BY e.event_time DESC
    LIMIT ${(page - 1) * limit}, ${limit}
  `;
  const [data] = await db.execute(sql.raw(sqlStr));
  return { data };
} 