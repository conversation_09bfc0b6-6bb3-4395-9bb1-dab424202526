apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nqhy-backend-ingress
  annotations:
    # 使用 Nginx Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    # 启用 SSL 重定向
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # 证书管理器（如果使用 cert-manager）
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
    # 客户端最大请求体大小
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    # 代理超时设置
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
spec:
  tls:
    - hosts:
        - localhost
      secretName: nqhy-tls-secret
  rules:
    - host: localhost
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nqhy-backend
                port:
                  number: 3000
