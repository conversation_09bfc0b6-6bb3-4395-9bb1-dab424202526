import type {
  DouyinMessageTask,
  TaskExecutionContext,
  TaskExecutionResult,
  TaskHandler,
} from '@/types/task-message'
import { schedulerLogger } from '@/lib/logger'
import {
  TaskType,
} from '@/types/task-message'
import { sendBatchSubscribeMessages } from './douyin-message'

/**
 * 抖音消息任务处理器
 */
export class DouyinMessageTaskHandler implements TaskHandler<DouyinMessageTask> {
  taskType = TaskType.DOUYIN_MESSAGE

  async handle(context: TaskExecutionContext): Promise<TaskExecutionResult> {
    const { taskMessage, startTime, logger } = context
    const task = taskMessage as DouyinMessageTask

    logger.info('开始执行抖音消息任务', {
      taskId: task.taskId,
      tplId: task.parameters.tplId,
      retryCount: context.retryCount,
    })

    try {
      // 转换为原有的任务格式
      const legacyTask = {
        tplId: task.parameters.tplId,
        messageData: task.parameters.messageData,
        enabled: task.parameters.enabled,
        cronExpression: task.parameters.cronExpression,
      }

      // 执行消息发送
      const result = await sendBatchSubscribeMessages(legacyTask)

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      logger.info('抖音消息任务执行完成', {
        taskId: task.taskId,
        tplId: task.parameters.tplId,
        totalUsers: result.totalUsers,
        successCount: result.results.filter(r => r.success).length,
        failCount: result.results.filter(r => !r.success).length,
        duration,
      })

      return {
        taskId: task.taskId,
        taskType: task.taskType,
        success: true,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        result: {
          totalUsers: result.totalUsers,
          successCount: result.results.filter(r => r.success).length,
          failCount: result.results.filter(r => !r.success).length,
          details: result.results,
        },
        retryCount: context.retryCount,
      }
    }
    catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      logger.error('抖音消息任务执行失败', {
        taskId: task.taskId,
        tplId: task.parameters.tplId,
        error: error.message,
        duration,
        retryCount: context.retryCount,
      })

      return {
        taskId: task.taskId,
        taskType: task.taskType,
        success: false,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        error: error.message,
        retryCount: context.retryCount,
      }
    }
  }

  validateParams(params: DouyinMessageTask['parameters']): boolean {
    return !!(
      params.tplId
      && params.messageData
      && Array.isArray(params.messageData)
      && params.cronExpression
    )
  }

  getMaxRetries(): number {
    return 3
  }

  getRetryDelay(retryCount: number): number {
    // 指数退避：1分钟、2分钟、4分钟
    return 2 ** retryCount * 60 * 1000
  }
}

/**
 * 数据同步任务处理器（示例）
 */
export class DataSyncTaskHandler implements TaskHandler {
  taskType = TaskType.DATA_SYNC

  async handle(context: TaskExecutionContext): Promise<TaskExecutionResult> {
    const { taskMessage, startTime, logger } = context

    logger.info('开始执行数据同步任务', {
      taskId: taskMessage.taskId,
      parameters: taskMessage.parameters,
    })

    try {
      // 这里实现具体的数据同步逻辑
      await this.performDataSync(taskMessage.parameters)

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      logger.info('数据同步任务执行完成', {
        taskId: taskMessage.taskId,
        duration,
      })

      return {
        taskId: taskMessage.taskId,
        taskType: taskMessage.taskType,
        success: true,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        result: { message: '数据同步完成' },
        retryCount: context.retryCount,
      }
    }
    catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      logger.error('数据同步任务执行失败', {
        taskId: taskMessage.taskId,
        error: error.message,
        duration,
      })

      return {
        taskId: taskMessage.taskId,
        taskType: taskMessage.taskType,
        success: false,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        error: error.message,
        retryCount: context.retryCount,
      }
    }
  }

  private async performDataSync(parameters: any): Promise<void> {
    // 模拟数据同步
    await new Promise(resolve => setTimeout(resolve, 1000))
    schedulerLogger.info('数据同步执行中', { parameters })
  }

  getMaxRetries(): number {
    return 2
  }

  getRetryDelay(retryCount: number): number {
    return retryCount * 30 * 1000 // 30秒、60秒
  }
}

/**
 * 清理任务处理器（示例）
 */
export class CleanupTaskHandler implements TaskHandler {
  taskType = TaskType.CLEANUP

  async handle(context: TaskExecutionContext): Promise<TaskExecutionResult> {
    const { taskMessage, startTime, logger } = context

    logger.info('开始执行清理任务', {
      taskId: taskMessage.taskId,
      parameters: taskMessage.parameters,
    })

    try {
      // 这里实现具体的清理逻辑
      await this.performCleanup(taskMessage.parameters)

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      return {
        taskId: taskMessage.taskId,
        taskType: taskMessage.taskType,
        success: true,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        result: { message: '清理任务完成' },
        retryCount: context.retryCount,
      }
    }
    catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      return {
        taskId: taskMessage.taskId,
        taskType: taskMessage.taskType,
        success: false,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        error: error.message,
        retryCount: context.retryCount,
      }
    }
  }

  private async performCleanup(parameters: any): Promise<void> {
    // 模拟清理操作
    await new Promise(resolve => setTimeout(resolve, 500))
    schedulerLogger.info('清理任务执行中', { parameters })
  }

  getMaxRetries(): number {
    return 1
  }
}

// 导出所有处理器
export const taskHandlers = {
  [TaskType.DOUYIN_MESSAGE]: new DouyinMessageTaskHandler(),
  [TaskType.DATA_SYNC]: new DataSyncTaskHandler(),
  [TaskType.CLEANUP]: new CleanupTaskHandler(),
}
