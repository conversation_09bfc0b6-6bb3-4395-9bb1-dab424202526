import { defineConfig } from 'drizzle-kit'

// 构建带时区的数据库 URL
function buildDatabaseUrl() {
  const baseUrl = process.env.DATABASE_URL!

  // 检查 URL 是否已经包含参数
  const separator = baseUrl.includes('?') ? '&' : '?'

  // 添加时区参数 (+08:00 URL 编码为 %2B08%3A00)
  return `${baseUrl}${separator}timezone=%2B08%3A00`
}

export default defineConfig({
  schema: './src/schema.ts',
  out: './drizzle',
  dialect: 'mysql',
  dbCredentials: {
    url: buildDatabaseUrl(),
  },
  verbose: true,
  strict: true,
})
