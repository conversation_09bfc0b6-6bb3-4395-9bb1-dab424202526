import type { ScheduledMessageTask } from '@/config'
import { getDb } from '@/db'
import { nqhyActiveUser } from '@/schema'
import type { BaseTaskResult } from '@/types/task-message'
import { and, eq, gte, lt, desc } from 'drizzle-orm'
import { mysqlTable, varchar, datetime, bigint } from 'drizzle-orm/mysql-core'
import { getChinaTime } from '@/utils/time'
import { getRedis } from '@/redis'

export async function activeUserCacheTemp(task: ScheduledMessageTask,count = 4): Promise<BaseTaskResult> {
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
    const now = getChinaTime();
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);

    const daysAgoStart = new Date(todayStart);
    daysAgoStart.setDate(todayStart.getDate() - 7);

    const currentMonth = now.getMonth() + 1; 
    const currentYear = now.getFullYear();
    const tableName = `xthy_event_data_${currentYear}_${currentMonth}`;

    console.log(`查询活跃用户，使用表: ${tableName}`);


    
    const eventTable = mysqlTable(tableName, {
      id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
      openId: varchar('open_id', { length: 255 }),
      eventCode: varchar('event_code', { length: 255 }),
      eventTime: datetime('event_time'),
      uuid: varchar('uuid', { length: 255 }),
      appId: varchar('app_id', { length: 255 })
    });

    const result = await db
      .select({
        openId: eventTable.openId,
        appId: eventTable.appId,
        eventTime: eventTable.eventTime
      })
      .from(eventTable)
      .innerJoin(nqhyActiveUser, and(eq(eventTable.openId, nqhyActiveUser.openId),eq(nqhyActiveUser.eventCode,`stage_end_stage_${count}`)))
      .where(and(
        gte(eventTable.eventTime, daysAgoStart),
        lt(eventTable.eventTime, todayStart),
        eq(eventTable.appId, task.appId)
      ))
      .orderBy(desc(eventTable.eventTime))
      .execute();
      const redis = await getRedis();
      const redisKey = `nqhy:activeOpenIds:${count}:${task.platform}:${task.appId}`;
      await redis.del(redisKey);
      let openIds = result.map(r => String(r.openId));
      if (openIds.length > 0) {
        console.log(`开始批量添加 ${openIds.length} 个活跃用户到 Redis Set`);
        const startTime = Date.now();

        await redis.sAdd(redisKey, openIds);

        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`Redis 批量 sAdd 操作完成，耗时: ${duration}ms，平均每个用户: ${(duration / openIds.length).toFixed(2)}ms`);
      }
      console.log("添加成功")
      return {
        totalUsers: result.length,
        results: result.map(r => ({ openId: r.openId, success: true, error: '' })),
        message: ""
      }
}
