import type { ScheduledMessageTask } from '@/config'
import { getDb } from '@/db'
import { xthyLoginData } from '@/schema'
import type { BaseTaskResult } from '@/types/task-message'
import { and, eq, lt, gte, max as drizzleMax } from 'drizzle-orm'
import { getChinaTime } from '@/utils/time'
import { getRedis } from '@/redis'

export async function sendFeedCache(task: ScheduledMessageTask): Promise<BaseTaskResult> {
const {
      tplId,
      messageData,
      page,
      max,
      appId
    } = task
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
    const now = getChinaTime();
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);

    const daysAgoStart = new Date(todayStart);
    daysAgoStart.setDate(todayStart.getDate() - 7);

    console.log("开始查询今天未登录的用户");
    const result = await db
      .select({
        openId: xthyLoginData.openId,
        maxCreateTime: drizzleMax(xthyLoginData.createTime).as('maxCreateTime')
      })
      .from(xthyLoginData)
      .where(eq(xthyLoginData.appId, appId))
      .groupBy(xthyLoginData.openId)
      .having(and(
        lt(drizzleMax(xthyLoginData.createTime), todayStart),
        gte(drizzleMax(xthyLoginData.createTime), daysAgoStart)
      ))
      .execute();
      const redis = await getRedis();
      const redisKey = `feed:inactiveOpenIds:${appId}`;
      await redis.del(redisKey);
      let openIds = result.map(r => String(r.openId));
      if (openIds.length > 0) {
        console.log(`开始批量添加 ${openIds.length} 个用户到 Redis Set`);
        const startTime = Date.now();

        await redis.sAdd(redisKey, openIds);

        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`Redis 批量 sAdd 操作完成，耗时: ${duration}ms，平均每个用户: ${(duration / openIds.length).toFixed(2)}ms`);
      }
      console.log("添加成功")
      return {
        totalUsers: result.length,
        results: result.map(r => ({ openId: r.openId, success: true, error: '' })),
        message: ""
      }
}
