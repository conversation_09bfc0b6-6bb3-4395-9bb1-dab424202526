import { getMessageTasks, ScheduledMessageTask, } from '@/config'
import { sendBatchSubscribeMessages } from '../services/douyin-message'
import { shouldExecuteTask } from '@/common'
import { distributedLock } from './distributed-lock'
import { schedulerLogger } from '@/lib/logger'
import { getDb } from '../db'
import { scheduledTasks } from '../schema'
import { eq } from 'drizzle-orm'
import { getChinaTime, formatChinaTime } from '@/utils/time'
import { sendBatchCards } from './card/sendCard'
import { BaseTaskResult } from '@/types/task-message'
import { sendFeedCache } from './feed/feedCache'
import { activeUserCache } from './active-user/active'




/**
 * 执行单个发送任务
 */
async function executeTask(task: ScheduledMessageTask) {
  try {
    if (!task.enabled) {
      schedulerLogger.info('任务已禁用，跳过执行', { tplId: task.tplId })
      return
    }

    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }

    // 检查任务是否今天已经执行过
    const taskInfo = await db
      .select()
      .from(scheduledTasks)
      .where(eq(scheduledTasks.tplId, task.tplId))
      .limit(1)
      .execute()

    if (taskInfo.length === 0) {
      throw new Error(`未找到模板ID为 ${task.tplId} 的任务配置`)
    }

    const taskRecord = taskInfo[0]
    const now = getChinaTime()

    // 检查任务是否今天已经执行过
    if (taskRecord.lastExecutedAt) {
      const lastExecuteDate = new Date(taskRecord.lastExecutedAt)
      const currentDate = now

      // 比较日期部分，如果是同一天则跳过
      const lastDateString = lastExecuteDate.toDateString()
      const currentDateString = currentDate.toDateString()

      if (lastDateString === currentDateString) {
        schedulerLogger.info(`任务 ${task.tplId} 今天已经执行过，跳过推送`)
        schedulerLogger.info(`上次执行时间: ${formatChinaTime(taskRecord.lastExecutedAt)} (CST)`)
        schedulerLogger.info(`上次执行日期: ${lastDateString}`)
        schedulerLogger.info(`当前日期: ${currentDateString}`)
        return
      }
    }

    try {
      schedulerLogger.info('开始执行任务', { tplId: task.tplId, taskType: taskRecord.taskType })

      let result:BaseTaskResult
      // 根据任务类型选择执行函数
      if (taskRecord.taskType === 'card') {
        result = await sendBatchCards(task)
      } else if (taskRecord.taskType === 'message'){
        // 默认为消息任务
        result = await sendBatchSubscribeMessages(task)
      }else if (taskRecord.taskType === 'feed'){
        // 默认为消息任务
        result = await sendFeedCache(task)
      }else if (taskRecord.taskType === 'active-user'){
        // 默认为消息任务
        result = await activeUserCache(task)
      }


      // 推送完成后，更新任务的 lastExecutedAt 时间，标记已执行
      const updateTime = getChinaTime()
      await db
        .update(scheduledTasks)
        .set({
          lastExecutedAt: updateTime,
          updatedAt: updateTime
        })
        .where(eq(scheduledTasks.tplId, task.tplId))
        .execute()
      
      schedulerLogger.info(`任务 ${task.tplId} 推送完成，更新执行时间: ${formatChinaTime(updateTime)} (CST)`)

      schedulerLogger.info('任务执行完成', {
        tplId: task.tplId,
        taskType: taskRecord.taskType,
        totalUsers: result.totalUsers,
        successCount: result.results?.filter(r => r.success).length || 0,
        failCount: result.results?.filter(r => !r.success).length || 0
      })
    } catch (error) {
      schedulerLogger.error('任务执行失败', {
        tplId: task.tplId,
        error: error.message
      })
    }
  } catch (error) {
    schedulerLogger.error('获取分布式锁失败', {
      tplId: task.tplId,
      error: error.message
    })
  }
}


/**
 * 检查并执行所有到期的定时任务
 */
export async function checkAndExecuteScheduledTasks() {
  const schedulerLockKey = 'scheduler_master_lock'
  const lockTTL = 30 // 30秒锁
  const locked = await distributedLock.acquire(schedulerLockKey, lockTTL)
  if (!locked) {
    schedulerLogger.debug('其他实例正在执行调度，跳过,schedulerLockKey->',schedulerLockKey)
    return
  }
  schedulerLogger.debug('检查定时任务', { timestamp: new Date().toISOString() })

  // 从数据库加载最新的任务配置
  const messageTasks = await getMessageTasks()

  if (messageTasks.length === 0) {
    schedulerLogger.debug('没有找到启用的定时任务')
    return
  }

  for (const task of messageTasks) {
    if (shouldExecuteTask(task.cronExpression)) {
      await executeTask(task)
      // 使用分布式锁执行任务，防止多实例重复执行
      // await distributedLock.executeWithLock(
      //   lockKey,
      //   async () => {
      //     console.log(1111)

      //   },
      //   600 // 10分钟锁过期时间，给任务足够的执行时间
      // )
    }
  }
  await distributedLock.release(schedulerLockKey)
}

/**
 * 启动定时任务调度器
 * 每分钟检查一次是否有任务需要执行
 */
export function startScheduler(schedulerMode:string) {
  // 检查是否启用定时任务
  if (process.env.SCHEDULER_ENABLED === 'false') {
    schedulerLogger.info('定时任务调度器已禁用')
    return
  }

  // 检查是否为主实例（可选配置）
  // const isMainInstance = process.env.SCHEDULER_MAIN_INSTANCE !== 'false'
  // if (!isMainInstance) {
  //   schedulerLogger.info('当前实例不是主实例，跳过启动定时任务调度器')
  //   return
  // }
  const kafkaEnabled = process.env.KAFKA_ENABLED === 'true' && process.env.SCHEDULER_MODE === 'kafka'

  schedulerLogger.info('启动定时任务调度器', {
    nodeEnv: process.env.NODE_ENV,
    instanceId: process.env.INSTANCE_ID || 'unknown',
    kafkaEnabled,
    schedulerMode,
  })
  // 立即执行一次检查
  checkAndExecuteScheduledTasks()

  // 每分钟检查一次
  const intervalId = setInterval(() => {
    checkAndExecuteScheduledTasks()
  }, 60 * 1000)
}

/**
 * 手动触发特定模板的消息发送
 */
export async function triggerMessageTask(tplId: string, customData?: Record<string, string>[]) {
  // 从数据库加载最新的任务配置
  const messageTasks = await getMessageTasks()
  const task = messageTasks.find((t: ScheduledMessageTask) => t.tplId === tplId)
  if (!task) {
    throw new Error(`未找到模板ID为 ${tplId} 的任务配置`)
  }

  const taskToExecute: ScheduledMessageTask = {
    ...task,
    messageData: customData || task.messageData
  }

  // 手动触发的任务也使用分布式锁，防止与定时任务冲突
  const lockKey = `manual_task_${task.tplId}_${Date.now()}`

  await distributedLock.executeWithLock(
    lockKey,
    async () => {
      await executeTask(taskToExecute)
    },
    60 // 1分钟锁过期时间
  )
}

// 导出获取任务配置的函数
export { getMessageTasks }