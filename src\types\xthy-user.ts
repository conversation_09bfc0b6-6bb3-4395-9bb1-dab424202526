/**
 * 星通互娱用户信息相关类型定义
 */

import { z } from 'zod'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { xthyUserData } from '@/schema'

// 从数据库 schema 生成的类型
export type XthyUserData = typeof xthyUserData.$inferSelect
export type NewXthyUserData = typeof xthyUserData.$inferInsert

// Zod 验证 schema
export const insertXthyUserDataSchema = createInsertSchema(xthyUserData, {
  nonId: z.number().optional(), // 自增ID，插入时可选
  appId: z.string().optional(),
  openId: z.string().max(64).optional(),
  unionId: z.string().optional(),
  latestAdId: z.string().optional(),
  latestClickId: z.string().max(300).optional(),
  clickIdCallbackFlag: z.enum(['Y', 'N']).default('N'),
  createTime: z.date().optional(),
  platform: z.string().max(50).optional(),
  updateTime: z.date().optional(),
  totalIpu: z.number().default(0),
  totalCost: z.number().default(0),
  advertiserId: z.string().max(30).default('999999'),
  postBackTime: z.date().optional(),
  openGm: z.number().min(0).max(1).default(0),
  openFreeAd: z.number().min(0).max(1).default(0),
})

export const selectXthyUserDataSchema = createSelectSchema(xthyUserData)

// API 请求/响应类型
export interface CreateXthyUserRequest {
  appId?: string
  openId?: string
  unionId?: string
  latestAdId?: string
  latestClickId?: string
  platform?: string
}

export interface UpdateXthyUserRequest {
  latestAdId?: string
  latestClickId?: string
  clickIdCallbackFlag?: 'Y' | 'N'
  platform?: string
  totalIpu?: number
  totalCost?: number
  advertiserId?: string
  postBackTime?: string | Date
  openGm?: 0 | 1
  openFreeAd?: 0 | 1
}

export interface XthyUserResponse {
  nonId: number
  appId?: string
  openId?: string
  unionId?: string
  latestAdId?: string
  latestClickId?: string
  clickIdCallbackFlag: string
  createTime: Date
  platform?: string
  updateTime: Date
  totalIpu: number
  totalCost: number
  advertiserId: string
  postBackTime?: Date
  openGm: number
  openFreeAd: number
}

// 查询参数类型
export interface XthyUserQueryParams {
  openId?: string
  unionId?: string
  appId?: string
  platform?: string
  clickIdCallbackFlag?: 'Y' | 'N'
  advertiserId?: string
  openGm?: 0 | 1
  openFreeAd?: 0 | 1
  startTime?: string | Date
  endTime?: string | Date
  page?: number
  pageSize?: number
}

// 用户统计类型
export interface XthyUserStats {
  totalUsers: number
  callbackUsers: number
  callbackRate: number
  gmUsers: number
  freeAdUsers: number
  platformStats: Array<{
    platform: string
    count: number
  }>
  advertiserStats: Array<{
    advertiserId: string
    count: number
  }>
}

// 签到奖励相关类型
export interface SignInReward {
  openId: string
  dayIndex: number // 签到天数索引（1-7）
  createTime: Date
  rewardData: Record<string, string> // 奖励数据
}
