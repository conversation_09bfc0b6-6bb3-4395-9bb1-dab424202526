apiVersion: apps/v1
kind: Deployment
metadata:
  name: nqhy-backend
spec:
  replicas: 3 # 运行3个实例
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1 # 最多1个Pod不可用
      maxSurge: 1 # 最多额外创建1个Pod
  selector:
    matchLabels:
      app: nqhy-backend
  template:
    metadata:
      labels:
        app: nqhy-backend
    spec:
      imagePullSecrets:
        - name: nqhy-registry-secret
      containers:
        - name: app
          image: nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-site:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
            # 健康检查配置
          livenessProbe:
            httpGet:
              path: /ping
              port: 3000
              scheme: HTTPS
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ping
              port: 3000
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          # 重启策略
          restartPolicy: Always
          env:
            - name: NODE_ENV
              value: "production"
            - name: PORT
              value: "3000"
            - name: ENVIRONMENT
              value: "production"
            - name: TZ
              value: "Asia/Shanghai"
            - name: HTTPS_ENABLED
              value: "true"
            - name: LOG_LEVEL
              value: "info"
            - name: LOG_FORMAT
              value: "json"
            - name: DATABASE_URL
              value: "mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007"
            - name: REDIS_HOST
              value: "127.0.0.1"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              value: "cnmb@Gunsb!"
            - name: KAFKA_ENABLED
              value: "false"
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "kafka:9092"
            - name: KAFKA_USERNAME
              value: "admin"
            - name: KAFKA_PASSWORD
              value: "12345678"
            - name: KAFKA_GROUP_ID
              value: "nqhy-backend-group"
            - name: SCHEDULER_ENABLED
              value: "true"
            - name: SCHEDULER_MAIN_INSTANCE
              value: "true"
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"
---
apiVersion: v1
kind: Service
metadata:
  name: nqhy-backend
spec:
  selector:
    app: nqhy-backend
  ports:
    - port: 3000
      targetPort: 3000
  type: LoadBalancer
