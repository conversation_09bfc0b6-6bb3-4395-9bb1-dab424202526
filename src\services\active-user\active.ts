import type { ScheduledMessageTask } from '@/config'
import { getDb } from '@/db'
import { nqhyActiveUser } from '@/schema'
import type { BaseTaskResult } from '@/types/task-message'
import { and, eq, sql } from 'drizzle-orm'
import { mysqlTable, varchar, datetime, bigint } from 'drizzle-orm/mysql-core'
import { getChinaTime } from '@/utils/time'
import { getRedis } from '@/redis'

export async function activeUserCache(task: ScheduledMessageTask): Promise<BaseTaskResult> {
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
    const now = getChinaTime();

    const currentMonth = now.getMonth() + 1; 
    const currentYear = now.getFullYear();
    const tableName = `xthy_event_data_${currentYear}_${currentMonth}`;
    if (!task.meta?.count) {
      throw new Error('任务元数据中未找到 count 字段')
    }
    const count = task.meta.count 
    console.log(`处理活跃用户数据，使用表: ${tableName}`);

    const eventTable = mysqlTable(tableName, {
      id: bigint('id', { mode: 'number' }).primaryKey().autoincrement(),
      openId: varchar('open_id', { length: 255 }),
      eventCode: varchar('event_code', { length: 255 }),
      eventTime: datetime('event_time'),
      uuid: varchar('uuid', { length: 255 }),
      appId: varchar('app_id', { length: 255 })
    });

    const eventData = await db
      .select({
        openId: eventTable.openId,
        eventCode: eventTable.eventCode,
        eventTime: eventTable.eventTime,
        uuid: eventTable.uuid,
        appId: eventTable.appId
      })
      .from(eventTable)
      .where(and(
        eq(eventTable.eventCode, `stage_end_stage_${count}`),
        eq(eventTable.appId, task.appId)
      ))
      .execute();

    console.log(`找到 ${eventData.length} 条符合条件的事件数据`);

    if (eventData.length === 0) {
      return {
        totalUsers: 0,
        results: [],
        message: "没有找到符合条件的事件数据"
      }
    }

    const insertResult = await db
      .insert(nqhyActiveUser)
      .values(eventData.map(event => ({
        openId: event.openId,
        eventCode: event.eventCode,
        eventTime: event.eventTime,
        uuid: event.uuid,
        appId: event.appId
      })))
      .onDuplicateKeyUpdate({
        set: {
          eventTime: sql`values(${nqhyActiveUser.eventTime})`,
          uuid: sql`values(${nqhyActiveUser.uuid})`
        }
      })
      .execute();

    console.log(`成功处理 ${eventData.length} 条活跃用户数据，影响行数: ${insertResult.affectedRows}`);

    // 更新Redis缓存
    const redis = await getRedis();
    const redisKey = `nqhy:activeOpenIds:${count}:${task.platform}:${task.appId}`;
    await redis.del(redisKey);

    let openIds = eventData.map(r => String(r.openId));
    if (openIds.length > 0) {
      console.log(`开始批量添加 ${openIds.length} 个活跃用户到 Redis Set`);
      const startTime = Date.now();

      await redis.sAdd(redisKey, openIds);

      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`Redis 批量 sAdd 操作完成，耗时: ${duration}ms，平均每个用户: ${(duration / openIds.length).toFixed(2)}ms`);
    }

    console.log("处理完成")
    return {
      totalUsers: eventData.length,
      results: eventData.map(r => ({ openId: String(r.openId), success: true, error: '' })),
      message: `成功处理 ${eventData.length} 条活跃用户数据`
    }
}
