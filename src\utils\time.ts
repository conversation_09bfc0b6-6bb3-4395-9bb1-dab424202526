/**
 * 时间工具函数
 */
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// 启用插件
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * 格式化时间为中国时区字符串
 * @param date Date 对象
 * @returns 格式化的中国时区时间字符串
 */
export function formatChinaTime(date: Date): string {
  const dayjsDate = dayjs(date)
  if (!dayjsDate.isValid()) {
    throw new Error('Invalid date provided to formatChinaTime')
  }
  return dayjsDate.tz('Asia/Shanghai').add(-8, 'hour').format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化时间为中国时区 ISO 格式字符串
 * @param date Date 对象
 * @returns 格式化的中国时区 ISO 时间字符串
 */
export function formatChinaTimeISO(date: Date): string {
  const dayjsDate = dayjs(date)
  if (!dayjsDate.isValid()) {
    throw new Error('Invalid date provided to formatChinaTimeISO')
  }
  return dayjsDate.tz('Asia/Shanghai').toISOString()
}

/**
 * 获取当前中国时区时间
 * @returns 当前中国时区时间的 Date 对象
 */
export function getChinaTime(date: dayjs.ConfigType = null): Date {
  return getChinaDayJs(date).toDate()
}

/**
 * 安全获取中国时区时间，如果输入无效则返回当前时间
 * @param date 输入日期
 * @returns 中国时区时间的 Date 对象
 */
export function getChinaTimeSafe(date: dayjs.ConfigType = null): Date {
  try {
    return getChinaDayJs(date).toDate()
  } catch (error) {
    // 如果输入无效，返回当前时间
    return getChinaDayJs().toDate()
  }
}

export function getChinaDayJs(date: dayjs.ConfigType = null): Dayjs {
  const dayjsDate = date ?dayjs(date) :dayjs()
  if (!dayjsDate.isValid()) {
    throw new Error(`Invalid date provided to getChinaDayJs: ${date}`)
  }
  return dayjsDate.tz('Asia/Shanghai').add(8, 'hour')
}

/**
 * 判断给定日期是否是今天（中国时区） 
 * @param date 要比较的日期
 * @returns 如果是今天返回 true，否则返回 false
 */
export function isToday(date: Date | string | number): boolean {
  try {
    const targetDate = getChinaDayJs(date)
    const today = getChinaDayJs()
    return targetDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')
  } catch (error) {
    return false
  }
}

/**
 * 判断给定日期是否是之前的日期（中国时区）
 * @param date 要比较的日期
 * @returns 如果是之前的日期返回 true，否则返回 false
 */
export function isPreviousDate(date: Date | string | number): boolean {
  try {
    const targetDate = getChinaDayJs(date)
    const today = getChinaDayJs()
    return targetDate.isBefore(today, 'day')
  } catch (error) {
    return false
  }
}

/**
 * 判断给定日期是否是未来的日期（中国时区）
 * @param date 要比较的日期
 * @returns 如果是未来的日期返回 true，否则返回 false
 */
export function isFutureDate(date: Date | string | number): boolean {
  try {
    const targetDate = getChinaDayJs(date)
    const today = getChinaDayJs()
    return targetDate.isAfter(today, 'day')
  } catch (error) {
    return false
  }
}

/**
 * 获取两个日期之间的天数差（中国时区）
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 天数差（date1 - date2），正数表示 date1 在 date2 之后
 */
export function getDaysDifference(date1: Date | string | number, date2: Date | string | number): number {
  try {
    const d1 = getChinaDayJs(date1)
    const d2 = getChinaDayJs(date2)
    return d1.diff(d2, 'day')
  } catch (error) {
    return 0
  }
}
