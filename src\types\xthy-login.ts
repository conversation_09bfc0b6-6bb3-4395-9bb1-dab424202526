/**
 * 星通互娱登录事件相关类型定义
 */

import { z } from 'zod'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { xthyLoginData } from '@/schema'

// 从数据库 schema 生成的类型
export type XthyLoginData = typeof xthyLoginData.$inferSelect
export type NewXthyLoginData = typeof xthyLoginData.$inferInsert

// Zod 验证 schema
export const insertXthyLoginDataSchema = createInsertSchema(xthyLoginData, {
  nonId: z.number().optional(), // 自增ID，插入时可选
  appId: z.string().optional(),
  openId: z.string().min(1, 'openId不能为空'),
  clickId: z.string().optional(),
  clickIdCallbackFlag: z.enum(['Y', 'N']).default('N'),
  adId: z.string().optional(),
  clickTime: z.date().optional(),
  createTime: z.date().optional(),
  platform: z.string().max(50).optional(),
  updateTime: z.date().optional(),
  postBackTime: z.date().optional(),
})

export const selectXthyLoginDataSchema = createSelectSchema(xthyLoginData)

// API 请求/响应类型
export interface CreateXthyLoginRequest {
  appId?: string
  openId: string
  clickId?: string
  adId?: string
  clickTime?: string | Date
  platform?: string
}

export interface UpdateXthyLoginRequest {
  clickIdCallbackFlag?: 'Y' | 'N'
  postBackTime?: string | Date
  updateTime?: string | Date
}

export interface XthyLoginResponse {
  nonId: number
  appId?: string
  openId: string
  clickId?: string
  clickIdCallbackFlag: string
  adId?: string
  clickTime?: Date
  createTime: Date
  platform?: string
  updateTime: Date
  postBackTime?: Date
}

// 查询参数类型
export interface XthyLoginQueryParams {
  openId?: string
  clickId?: string
  appId?: string
  platform?: string
  clickIdCallbackFlag?: 'Y' | 'N'
  startTime?: string | Date
  endTime?: string | Date
  page?: number
  pageSize?: number
}

// 统计类型
export interface XthyLoginStats {
  totalLogins: number
  callbackCount: number
  callbackRate: number
  platformStats: Array<{
    platform: string
    count: number
  }>
}
