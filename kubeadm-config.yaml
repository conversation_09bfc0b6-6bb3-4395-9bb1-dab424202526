apiVersion: kubeadm.k8s.io/v1beta4
kind: InitConfiguration
localAPIEndpoint:
  advertiseAddress: "0.0.0.0"  # 替换为您的实际 IP 地址
  bindPort: 6443
nodeRegistration:
  criSocket: unix:///var/run/containerd/containerd.sock  # 或 unix:///var/run/crio/crio.sock
  kubeletExtraArgs:
    cgroup-driver: systemd
---
apiVersion: kubeadm.k8s.io/v1beta4
kind: ClusterConfiguration
kubernetesVersion: v1.32.0  # 使用支持的版本
clusterName: nqhy-cluster
controlPlaneEndpoint: "localhost:6443"  # 替换为您的实际地址
networking:
  serviceSubnet: "*********/12"
  podSubnet: "**********/16"  # 适用于 Flannel CNI
  dnsDomain: "cluster.local"
apiServer:
  advertiseAddress: "0.0.0.0"  # 替换为您的实际 IP 地址
  bindPort: 6443
  certSANs:
    - "localhost"
    - "127.0.0.1"
    - "0.0.0.0"  # 添加您的实际 IP 地址
etcd:
  local:
    dataDir: "/var/lib/etcd"
controllerManager: {}
scheduler: {}
dns:
  type: CoreDNS
---
apiVersion: kubelet.config.k8s.io/v1beta1
kind: KubeletConfiguration
cgroupDriver: systemd
failSwapOn: false
---
apiVersion: kubeproxy.config.k8s.io/v1alpha1
kind: KubeProxyConfiguration
mode: "iptables"
