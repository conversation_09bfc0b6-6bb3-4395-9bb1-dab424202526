import type { Context, Next } from 'hono'
import { apiLogger } from '@/lib/logger'

/**
 * 请求日志中间件
 * 记录所有HTTP请求的详细信息
 */
export function requestLogger() {
  return async (c: Context, next: Next) => {
    const start = Date.now()
    const method = c.req.method
    const url = c.req.url
    const userAgent = c.req.header('user-agent')
    const contentType = c.req.header('content-type')
    const requestId = c.get('requestId') || 'unknown'

    // 记录请求开始
    apiLogger.info('请求开始', {
      requestId,
      method,
      url,
      userAgent,
      contentType,
    })

    try {
      await next()

      const duration = Date.now() - start
      const status = c.res.status

      // 根据状态码选择日志级别
      if (status >= 500) {
        apiLogger.error('请求完成 - 服务器错误', {
          requestId,
          method,
          url,
          status,
          duration,
        })
      }
      else if (status >= 400) {
        apiLogger.warn('请求完成 - 客户端错误', {
          requestId,
          method,
          url,
          status,
          duration,
        })
      }
      else {
        apiLogger.info('请求完成 - 成功', {
          requestId,
          method,
          url,
          status,
          duration,
        })
      }
    }
    catch (error) {
      const duration = Date.now() - start

      apiLogger.error('请求异常', {
        requestId,
        method,
        url,
        duration,
        error: error.message,
        stack: error.stack,
      })

      throw error
    }
  }
}

/**
 * 错误日志中间件
 * 专门记录未捕获的错误
 */
export function errorLogger() {
  return async (c: Context, next: Next) => {
    try {
      await next()
    }
    catch (error) {
      const requestId = c.get('requestId') || 'unknown'

      apiLogger.error('未捕获的错误', {
        requestId,
        method: c.req.method,
        url: c.req.url,
        error: error.message,
        stack: error.stack,
      })

      throw error
    }
  }
}
